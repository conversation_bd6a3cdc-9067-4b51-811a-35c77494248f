"use client";

import dynamic from "next/dynamic";
import { useMemo } from "react";
import Toolbar from "@/app/[root]/notes/_components/note-toolbar";
import Cover from "@/app/[root]/notes/_components/note-cover";
import { Skeleton } from "@relio/ui/skeleton";
import type { Id } from "@relio/backend/convex/_generated/dataModel";
import { useUpdateNote } from "@/hooks/notes/use-update-note";
import { useGetNoteById } from "@/hooks/notes/use-get-note-by-id";

interface NoteIdPageParams {
  params: {
    id: Id<"notes">;
  }
}

const NoteIdPage = ({ params }: NoteIdPageParams) => {
  const Editor = useMemo(
    () => dynamic(() => import("@/app/[root]/notes/_components/editor"), { ssr: false }),
    []
  );

  const { note } = useGetNoteById({ id: params.id });
  const { mutate: update } = useUpdateNote();

  const onChange = (content: string) => {
    update({
      id: params.id,
      content,
    });
  };

  if (note === undefined) {
    return (
      <div>
        <Cover.Skeleton />
        <div className="md:max-w-3xl lg:max-w-4xl mx-auto mt-10">
          <div className="space-y-4 pl-8 pt-4">
            <Skeleton className="h-14 w-[50%]" />
            <Skeleton className="h-4 w-[80%]" />
            <Skeleton className="h-4 w-[40%]" />
            <Skeleton className="h-4 w-[60%]" />
          </div>
        </div>
      </div>
    );
  }

  if (note === null) {
    return <div>Not found</div>;
  }

  if (!note?.isPublished) {
    return <div>Error</div>;
  }

  return (
    <div>
      <div className="pb-40">
        <Cover preview url={note.coverImage} />
        <div className="md:max-w-3xl lg:max-w-4xl mx-auto">
          <Toolbar preview initialData={note} />
          <Editor
            editable={false}
            onChange={onChange}
            initialContent={note.content}
          />
        </div>
      </div>
    </div>
  );
};

export default NoteIdPage;