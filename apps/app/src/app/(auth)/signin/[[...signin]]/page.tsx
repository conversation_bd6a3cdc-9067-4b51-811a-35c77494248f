"use client";

import { SignInForm } from "@relio/ui/auth/SignInForm";
import { Authenticated, AuthLoading, Unauthenticated } from "convex/react";
import RootPage from "@/app/[root]/page";
import Loading from "@relio/ui/loading";

const Signin = () => {
  return (
    <div>
      <AuthLoading>
        <Loading />
      </AuthLoading>
      <Authenticated>
        <RootPage />
      </Authenticated>
      <Unauthenticated>
        <SignInForm />
      </Unauthenticated>
    </div>
  );
}

export default Signin;
