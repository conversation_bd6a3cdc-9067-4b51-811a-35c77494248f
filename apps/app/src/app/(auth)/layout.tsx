import RootLayout from "@relio/ui/layout/root-layout";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";
import { fetchQuery } from "convex/nextjs";
import { api } from "@relio/backend/convex/_generated/api";
import { redirect } from "next/navigation";

export default async function AuthLayout({ children }: { children: JSX.Element }) {
  return (
    <RootLayout>
      {children}
    </RootLayout>
  )
}
