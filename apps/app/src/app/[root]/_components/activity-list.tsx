import React from 'react';
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipTrigger,
} from "@relio/ui/tooltip"
import { formatDistanceToNow } from 'date-fns';
import getActivityIcon from "@relio/ui/custom/get-activity-icon";
import UserActivity from "./user-activity";
import type { Id } from "@relio/backend/convex/_generated/dataModel";
import { Organization } from '@relio/types';
import { useGetActivities } from '@relio/app/src/hooks/activities/use-get-activities';

type ActivityListProps = {
	data: any;
	user: any;
	organization: Organization;
	isEdit?: any;
	handleStateChange: any;
};

const ActivityList = ({ data, user, organization, isEdit, handleStateChange }: ActivityListProps) => {
	const recordId = data?._id as Id<"contacts"> | Id<"properties"> | Id<"companies"> | undefined;
	const { data: activities } = useGetActivities(recordId);

	console.log("activities 26", activities);

	return (
		<div className='space-y-4 py-3'>
			{activities?.map((activity: any, index: number) => {
				return (
					activity.system ? (
						<div key={index} className='flex items-center space-x-3'>
							{getActivityIcon(activity.type)}
							<div className='text-sm'>
								{activity.message}
							</div>
							<div className='text-xs text-gray-500'>
								<Tooltip>
									<TooltipTrigger className='cursor-default'>
										{formatDistanceToNow(new Date(activity._creationTime), { addSuffix: true })}
									</TooltipTrigger>
									<TooltipContent side='bottom'>
										{
											new Date(activity?._creationTime).toLocaleString('en-US', {
												month: 'long',
												day: 'numeric',
												year: 'numeric',
												hour: 'numeric',
												minute: 'numeric',
												hour12: true
											})
										}
									</TooltipContent>
								</Tooltip>
							</div>
						</div>
					) : (
						<div key={index}>
							<UserActivity activity={activity} organization={organization} isEdit={isEdit} handleStateChange={handleStateChange} />
						</div>
					)
				)
			})}
		</div>
	)
}

export default ActivityList;
