import {format,isValid} from "date-fns";
import UserCard from "@relio/ui/custom/user-card";

type MetadataProps = {
  data: any;
}

type FormattedDateProps = {
  dateString: string;
};

const Metadata = ({ data }: MetadataProps) => {
  const FormattedDate = ({ dateString }: FormattedDateProps) => {
    const date = new Date(dateString);
    const formattedDate = isValid(date) ? format(date, "PPPp") : "Invalid date";

    return <span>{formattedDate}</span>;
  };

  return (
      <div className="space-y-4">
        {/* Created Time */}
        <div className='grid grid-row-2 gap-2'>
          <div className={'flex flex-row items-center'}>
            <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
              Created
            </div>
            <div className="text-sm font-normal">
              <FormattedDate dateString={data?._creationTime} />
            </div>
          </div>
        </div>

        {/* Created By */}
        <div className='grid grid-row-2 gap-2'>
          <div className={'flex flex-row items-center'}>
            <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
              Created By
            </div>
            <div className="text-sm font-normal flex flex-row items-center">
              <UserCard user={data?.createdBy} />
              {data?.createdBy?.name}
            </div>
          </div>
        </div>

        {/* Viewed At */}
        <div className='grid grid-row-2 gap-2'>
          <div className={'flex flex-row items-center'}>
            <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
              Last Viewed
            </div>
            <div className="text-sm font-normal flex flex-row items-center">
              <FormattedDate dateString={data?.lastViewedTime} />
            </div>
          </div>
        </div>

        {/* Last Viewed By */}
        <div className='grid grid-row-2 gap-2'>
          <div className={'flex flex-row items-center'}>
            <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
              Viewed By
            </div>
            <div className="text-sm font-normal flex flex-row items-center">
              <UserCard user={data?.lastViewedBy} />
              {data?.lastViewedBy?.name}
            </div>
          </div>
        </div>

        {/* Updated At */}
        {data?.updatedTime && (
        <div className='grid grid-row-2 gap-2'>
          <div className={'flex flex-row items-center'}>
            <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
              Updated At
            </div>
            <div className="text-sm font-normal flex flex-row items-center">
              <FormattedDate dateString={data?.updatedTime} />
            </div>
            </div>
          </div>
        )}

        {/* Updated By */}
        {data?.updatedBy && (
        <div className='grid grid-row-2 gap-2'>
          <div className={'flex flex-row items-center'}>
            <div className="ml-2 w-[20%] text-[10px] uppercase font-normal text-gray-500">
              Updated By
            </div>
            <div className="text-sm font-normal flex flex-row items-center">
              <UserCard user={data?.updatedBy} />
              {data?.updatedBy?.name}
            </div>
            </div>
          </div>
        )}

      </div>
  );
}

export default Metadata;