import { cn } from '@relio/ui/utils';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  Ref,
} from 'react';

interface MentionListProps {
  items: Array<{ id: string; label: string }>;
  command: (item: { id: string; label: string }) => void;
}

type MentionListHandle = {
  onKeyDown: (event: { event: KeyboardEvent }) => boolean;
};

const MentionList = forwardRef<MentionListHandle, MentionListProps>(
  (props, ref) => {
    const [selectedIndex, setSelectedIndex] = useState(0);

    const selectItem = (index: number) => {
      const item = props.items[index];

      if (item) {
        props.command({ id: item.id, label: item.label });
      }
    };

    const upHandler = () => {
      setSelectedIndex(
        (selectedIndex + props.items.length - 1) % props.items.length
      );
    };

    const downHandler = () => {
      setSelectedIndex((selectedIndex + 1) % props.items.length);
    };

    const enterHandler = () => {
      selectItem(selectedIndex);
    };

    useEffect(() => setSelectedIndex(0), [props.items]);

    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }) => {
        if (event.key === 'ArrowUp') {
          upHandler();
          return true;
        }

        if (event.key === 'ArrowDown') {
          downHandler();
          return true;
        }

        if (event.key === 'Enter') {
          enterHandler();
          return true;
        }

        return false;
      },
    }));

    return (
      <div className="flex flex-col w-full z-50 min-w-[8rem] overflow-hidden rounded-lg border border-zinc-200 bg-white p-0.5 text-zinc-950 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-zinc-800 dark:secondary-bg dark:text-zinc-50">
        {props.items.length ? (
          props.items.map((item, index) => (
            <button
              className={cn(
                index === selectedIndex ? 'is-selected' : '',
                'flex justify-start items-center w-full p-1 text-sm text-left hover:bg-primary-100 dark:hover:bg-primary-800 hover:hover-bg rounded-lg'
              )}
              key={index}
              onClick={() => selectItem(index)}
            >
              {item.label}
            </button>
          ))
        ) : (
          <div className="flex justify-start items-center w-full p-1 text-sm text-left hover:bg-primary-100 dark:hover:bg-primary-800 rounded-lg">No user found.</div>
        )}
      </div>
    );
  }
);

MentionList.displayName = 'MentionList';

export { MentionList };