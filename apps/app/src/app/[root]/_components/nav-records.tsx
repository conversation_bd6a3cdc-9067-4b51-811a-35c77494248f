"use client"

import { NavItem } from "@relio/types"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@relio/ui/collapsible"
import { recordItems } from '@relio/ui/constants/navigation'
import getRecordIcon from "@relio/ui/custom/get-record-icon"
import { ChevronDownIcon, RecordsIcon } from "@relio/ui/icons"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem
} from "@relio/ui/sidebar"
import { cn } from "@relio/ui/utils"
import { setLocalStorage } from "@relio/ui/utils/local-storage"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"

export function NavRecords({
  slug
}: {
  slug: string;
}) {
  const pathname = usePathname();
  const router = useRouter();

  const handleViewChange = (viewType: string) => {
    const viewTypeLower = viewType.toLowerCase();
    const storageValue = viewTypeLower.includes('map') ? 'map' : 'list';
    
    console.log(`Setting view type: ${viewType} -> ${storageValue}`);
    setLocalStorage('relio:properties_view', storageValue);
    
    if (pathname.endsWith(`/${slug}/properties`)) {
      router.refresh();
    } else {
      router.push(`/${slug}/properties`);
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <Collapsible defaultOpen className="group/collapsible group-data-[collapsible=icon]:mb-6">
          <SidebarGroup>
            <SidebarGroupLabel className="group-data-[collapsible=icon]:opacity-100 !p-0">
              <CollapsibleTrigger className={cn(
                  "!h-6",
                  "peer/menu-button flex w-full items-center overflow-hidden rounded-md text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",
                  "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              )}>
                <div className="group-data-[collapsible=icon]:block hidden">
                  <RecordsIcon />
                </div>
                <div className="flex items-center gap-2 group-data-[collapsible=icon]:hidden px-2">
                  <ChevronDownIcon className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
                  <span className="text-xs">
                    Records
                  </span>
                </div>
              </CollapsibleTrigger>
            </SidebarGroupLabel>
            <CollapsibleContent>
              <SidebarMenu>
                {recordItems.map((item: NavItem, index: number) => {
                  const isActive = pathname.startsWith(`/${slug}/${item.recordType}`);

                  return (
                  <SidebarMenuItem key={index}>
                    <SidebarMenuButton 
                      tooltip={item.title}
                      className={cn(
                        "group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:items-center",
                      )}
                      asChild
                    >
                      <Link
                        href={`/${slug}/${item.recordType}`}
                        className={cn(
                          "transition-colors duration-200",
                          isActive && "bg-accent text-accent-foreground",
                          "hover:bg-accent hover:text-accent-foreground"
                        )}
                      >
                        {getRecordIcon(item.recordType as string)}
                        <span className="group-data-[collapsible=icon]:hidden">
                          {item.title}
                        </span>
                      </Link>
                    </SidebarMenuButton>
                    {item.items?.length ? (
                    <SidebarMenuSub>
                      {item.items.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild>
                            {item.recordType === 'properties' ? (
                              <Link 
                                href={`/${slug}/${item.recordType}`}
                                onClick={() => {
                                  handleViewChange(subItem.title);
                                }}
                              >
                                {getRecordIcon(subItem.icon as string)}
                                <span className="group-data-[collapsible=icon]:hidden">
                                  {subItem.title}
                                </span>
                              </Link>
                            ) : (
                              <a href={subItem.href}>{subItem.title}</a>
                            )}
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  ) : null}
                  </SidebarMenuItem>
                )})}
              </SidebarMenu>
            </CollapsibleContent>
          </SidebarGroup>
        </Collapsible>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
