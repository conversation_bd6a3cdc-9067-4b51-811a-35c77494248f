import React, { useState } from "react";
import { But<PERSON> } from "@relio/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@relio/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@relio/ui/popover";
import { CaretSortIcon } from "@radix-ui/react-icons";
import { cn } from "@relio/ui/utils";
import { AddIcon, CheckCircleIcon, CloseIcon, Icons } from "@relio/ui/icons";
import { Badge } from "@relio/ui/badge";
import {Id} from "@relio/backend/convex/_generated/dataModel";
import { useRouter } from "next/navigation";
import { Organization } from "@relio/types";
import { useCreateTag } from "@relio/app/src/hooks/tags/use-create-tag";
import { useGetTags } from "@relio/app/src/hooks/tags/use-get-tags";
import { useAddTagToRecord } from "@relio/app/src/hooks/tags/use-add-tag-to-record";
import { useRemoveTagFromRecord } from "@relio/app/src/hooks/tags/use-remove-tag-from-record";

type TagProps = {
  data: any;
  organization: Organization;
  recordType: "companies" | "contacts" | "properties";
};

const Tags = ({ data, organization, recordType }: TagProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>("");
  const router = useRouter();

  const { mutate: createTag } = useCreateTag();
  const { data: getTags } = useGetTags();
  const { mutate: addTagToRecord } = useAddTagToRecord();
  const { mutate: removeTagFromRecord } = useRemoveTagFromRecord();

  const tags = getTags || [];

  const recordTags = tags?.filter((tag) => data?.tags?.includes(tag._id));

  const handleCreateNew = async () => {
    try {
      await createTag({
        name: value,
        recordType: recordType,
        orgId: organization?._id as Id<"organization">,
        recordId: data._id
      })

      setOpen(false);
      setValue("");
    } catch (err) {
      console.error("Error creating tag", err);
    }
  };

  return (
    <div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {"Select or create tag..."}
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0 rounded-xl" side="right">
          <Command className="rounded-xl">
            <CommandInput
              placeholder="Search tag..."
              className="h-9"
              value={value}
              onValueChange={(value) => setValue(value)}
            />
            <CommandEmpty className={cn("p-1", value ? "h-10" : "h-8")}>
              <Button
                variant="secondary"
                className="w-full flex justify-start !h-8 rounded-lg"
                onClick={handleCreateNew}
              >
                <AddIcon className="mr-2" />
                Create {value}
              </Button>
            </CommandEmpty>
            <CommandGroup>
              <CommandList className={'h-fit'}>
                {tags?.map((tag: any) => (
                  <CommandItem
                    className="rounded-lg"
                    key={tag.name}
                    value={tag.name}
                    onSelect={(currentValue) => {
                      setValue(currentValue === value ? "" : currentValue);
                      addTagToRecord({ recordId: data._id, tagId: tag._id }).then();
                      setOpen(false);
                    }}
                  >
                    {tag.name}
                    <CheckCircleIcon
                      className={cn(
                        "ml-auto h-4 w-4",
                        data?.tags?.includes(tag?._id) ? "opacity-100 fill-indigo-700" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandList>
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {recordTags.length > 0 && (
        <div className="flex flex-wrap mt-2 space-x-2">
          {recordTags.map((tag: any, index: number) => (
            <Badge
              key={index}
              className={cn("tag-item flex flex-start px-2 py-0.5 cursor-pointer")}
              style={{
                backgroundColor: `${tag.color}50`,
                border: `1px solid ${tag.color}`,
              }}
              onClick={() => {
                router.push(`/${organization?.slug}/${recordType}/tag/${tag._id}`)
              }}
            >
              {tag.name}

              {tags.some((t) => t === tag) && (
                <div className="tag">
                  <CloseIcon
                    className="ml-1 h-4 w-4"
                    onClick={(e: any) => {
                      e.stopPropagation();
                      e.preventDefault();
                      removeTagFromRecord({ recordId: data._id, tagId: tag._id }).then();
                    }}
                  />
                </div>
              )}
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};

export default Tags;
