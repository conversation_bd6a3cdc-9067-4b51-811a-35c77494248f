"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Label } from "@relio/ui/label"
import { Switch } from "@relio/ui/switch"
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@relio/ui/select"
import { useE<PERSON><PERSON>, EditorContent, ReactRenderer, mergeAttributes } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder';
import UserCard from '@relio/ui/custom/user-card';
import { Button } from '@relio/ui/button';
import { CALL_RESULTS } from '@relio/ui/constants/data';
import { cn } from '@relio/ui/utils';
import Document from '@tiptap/extension-document'
import Mention from '@tiptap/extension-mention'
import Paragraph from '@tiptap/extension-paragraph'
import Text from '@tiptap/extension-text'
import type { Id } from "@relio/backend/convex/_generated/dataModel";
import tippy from 'tippy.js';
import { MentionList } from './mention-list';
import { useGetOrganizationMembers } from '@relio/app/src/hooks/organization/use-get-organization-members';
import { useCreateActivity } from '@relio/app/src/hooks/activities/use-create-activity';
import { useUpdateActivity } from '@relio/app/src/hooks/activities/use-update-activity';
import { Organization } from '@relio/types';

type ActivityEditorProps = {
	isEdit?: any;
	handleStateChange: any;
	user: any;
	organization: Organization;
	data: any;
	recordType: "contacts" | "properties" | "companies";
}

const ActivityEditor = ({ isEdit, handleStateChange, user, organization, data, recordType }: ActivityEditorProps) => {
	const { mutate: updateActivity } = useUpdateActivity();
	const { mutate: createActivity } = useCreateActivity();
	const { data: users } = useGetOrganizationMembers();
	const [value, setValue] = useState<string>('');
	const [callLog, setCallLog] = useState<boolean>(false);
	const [selectedPhone, setSelectedPhone] = useState<string>('');
	const [result, setResult] = useState<string>('');

	const memoizedSuggestion = useMemo(() => {
		return {
			items: ({ query }: { query: string }) => {
				if (!users) return [];
				const filteredUsers = users
				.map((user: any) => ({ id: user._id, label: user.name }))
				.filter((user: any) => user.label.toLowerCase().includes(query.toLowerCase()))
				.slice(0, 5);

				return filteredUsers;
			},
			render: () => {
				let component: ReactRenderer | null = null;
				let popup: any[] | null = null;

				return {
					onStart: (props: any) => {
						component = new ReactRenderer(MentionList, {
							props,
							editor: props.editor,
						})

						if (!props.clientRect) {
							return
						}

						popup = tippy('body', {
							getReferenceClientRect: props.clientRect,
							appendTo: () => document.body,
							content: component.element,
							showOnCreate: true,
							interactive: true,
							trigger: 'manual',
							placement: 'bottom-start',
						})
					},

					onUpdate(props: any) {
						component?.updateProps(props)

						if (!props.clientRect) {
							return
						}

						popup?.[0].setProps({
							getReferenceClientRect: props.clientRect,
						})
					},

					onKeyDown(props: any) {
						if (props.event.key === 'Escape') {
							popup?.[0].hide()
							return true
						}
						if (component?.ref && typeof component.ref === 'object' && 'onKeyDown' in component.ref) {
							return (component.ref.onKeyDown as (props: any) => boolean)(props);
						}
						return false;
					},

					onExit() {
						popup?.[0].destroy()
						component?.destroy()
					},
				}
			},
		};
	}, [users]);

	const editor = useEditor({
		extensions: [
			Document,
			Paragraph,
			Text,
			StarterKit,
			Placeholder.configure({
				placeholder: "Add note or select '/' to type a command",
			}),
			Mention.configure({
				HTMLAttributes: {
					class: 'mention',
				},
				suggestion: memoizedSuggestion,
				renderHTML({ options, node }: { options: any, node: any }) {
					return [
						'span',
						mergeAttributes({ class: 'mention' }, options.HTMLAttributes),
						`${options.suggestion.char}${node.attrs.label ?? node.attrs.id}`,
					]
				},
			})
		],
		content: value,
		onUpdate: ({ editor }) => {
			setValue(editor.getHTML());
		},
		immediatelyRender: false,
	})

	useEffect(() => {
		if (isEdit && editor) {
			editor.commands.setContent(isEdit.message)
			setCallLog(isEdit.type === "call");
		
			setSelectedPhone(data?.phone?.find((phone: any) => phone.number === isEdit.phone)?.number || '');
			setResult(CALL_RESULTS.includes(isEdit.result) ? isEdit.result : '');
		}
	}, [data, editor, isEdit]);

	const handleSave = async () => {
		try {
			const mentionedUserIds = editor?.getJSON().content
				?.flatMap(node => node.content ?? [])
				?.filter(node => node.type === 'mention')
				?.map(node => node?.attrs?.id as Id<"users">)
				?? [];

			if (!isEdit) {
				await createActivity({
					recordId: data?._id,
					type: callLog ? "call" : "note",
					message: value,
					phone: selectedPhone,
					result: result,
					mentionedUsers: mentionedUserIds,
					recordType
				})
			} else {
				await updateActivity({
					id: isEdit._id,
					recordId: data?._id,
					message: value,
					phone: selectedPhone,
					result: result,
					edited: isEdit.message !== value,
					editedTime: isEdit.message !== value ? new Date().toISOString() : undefined,
					mentionedUsers: mentionedUserIds,
					recordType,
				} as any)
			}

			if (editor) {
				editor.commands.clearContent();
			}

			handleStateChange('isEdit', false);

			setCallLog(false);
			setSelectedPhone('');
			setResult('');

		} catch (error) {
			console.error("Error adding activity:", error);
		}
	}

	const handleCancelEdit = () => {
		handleStateChange('isEdit', false);

		if (editor) {
			editor.commands.clearContent();
		}

		setCallLog(false);
		setSelectedPhone('');
		setResult('');
	}

	function extractMentionedUsers(content: string): string[] {
		const regex = /@(\w+)/g;
		const matches = content.match(regex);
		return matches ? matches.map(match => match.slice(1)) : [];
	}

	return (
		<div className="ql-wrapper">
			<div className='flex flex-row justify-start'>
				<div className='pt-2.5'>
					<UserCard user={user} />
				</div>
				<EditorContent
					className="tiptap h-full w-full mt-2.5 ml-2.5"
					editor={editor}
				/>
			</div>

			<div className="w-full flex items-center flex-row space-x-2 justify-between">
				<div className='left-side w-full flex flex-row items-center space-x-2'>
					{/* Switch */}
					{typeof window !== 'undefined' && window.location.pathname.includes('/contacts/') && (
						<div className='flex items-center space-x-2'>
							<Switch
								checked={callLog}
								onCheckedChange={() => setCallLog((prev) => !prev)}
								id="call-log"
								className="call-log-switch"
							/>
							<Label htmlFor="call-log" className='text-xs'>
								{callLog ? "Call Log" : "Note"}
							</Label>
						</div>
					)}

					{/* Phone Numbers */}
					{callLog && (
						<div className='flex items-center space-x-2'>
							<div>
								<Select
									value={selectedPhone}
									onValueChange={(value) => setSelectedPhone(value)}
								>
									<SelectTrigger className="w-full h-6 text-xs border-transparent hover:border-indigo-700 rounded-lg">
										<SelectValue placeholder="Phone Number" />
									</SelectTrigger>
									<SelectContent className='rounded-xl'>
										{data?.phone?.map((phone: any, index: number) => (
											<SelectItem value={phone?.number} key={index} className='rounded-lg'>
												{phone?.number}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<div>
								<Select
									value={result}
									onValueChange={(value) => setResult(value)}
								>
									<SelectTrigger className="w-full h-6 text-xs border-transparent hover:border-indigo-700 rounded-lg">
										<SelectValue placeholder="Result" />
									</SelectTrigger>
									<SelectContent className='rounded-xl'>
										{CALL_RESULTS.map((result, index) => (
											<SelectItem value={result} key={index} className='rounded-lg'>
												{result}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>
					)}
				</div>
				<div className='right-side'>
					<div className={'flex flex-row items-center gap-2'}>
						{isEdit && (
							<Button
								className={cn('!h-7 !bg-transparent hover:!hover-bg text-white rounded-lg text-xs')}
								onClick={handleCancelEdit}
							>
								Cancel
							</Button>
						)}
						<Button
							disabled={!value || value.trim() === "" || value === "<p><br></p>"}
							className={cn('!h-7 bg-indigo-700 hover:bg-indigo-900 text-white rounded-lg text-xs')}
							onClick={handleSave}
						>
							{isEdit ? "Update" : "Submit"}
						</Button>
					</div>
				</div>
			</div>
		</div>
	)
}

export default ActivityEditor;
