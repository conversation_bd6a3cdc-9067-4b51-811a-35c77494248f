"use client"

import { useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@relio/ui/sidebar"
import { NavItem } from "@relio/types"
import { getIcon } from "@relio/ui/icons"
import Notifications from "@relio/ui/notifications";
import { cn } from '@relio/ui/utils';
import { navItems } from '@relio/ui/constants/navigation';

const addSlugToNavItems = (navItems: NavItem[], slug: string): NavItem[] => {
	return navItems.map(item => ({
		...item,
		href: `/${slug}${item.href}`
	}));
};

export function NavMain({
  slug,
}: {
  slug: string
}) {
  const pathname = usePathname();
  const itemsWithSlug = useMemo(() => addSlugToNavItems(navItems, slug), [slug]);

  return (
    <SidebarGroup>
      <SidebarMenu>
        <Notifications />
        {itemsWithSlug.map((item) => {
          const isActive = pathname.startsWith(item.href as string);
          
          return (
            <Link
              key={item.title}
              href={item.href || '#'}
            >
              <SidebarMenuItem>
                <SidebarMenuButton 
                  tooltip={item.title}
                  className={cn(
                    "transition-colors duration-200",
                    isActive && "bg-accent text-accent-foreground",
                    "hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  {getIcon(item.icon as string, { 
                    className: cn(
                      "size-4",
                      isActive && "text-accent-foreground"
                    )
                  })}
                  <span>{item.title}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </Link>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
