import React, { useState } from 'react';
import ActivityEdit from "./activity-edit";
import { Tooltip, TooltipContent, TooltipTrigger } from "@relio/ui/tooltip";
import { Button } from "@relio/ui/button";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@relio/ui/utils";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import getActivityIcon from "@relio/ui/custom/get-activity-icon";
import AvatarCard from "@relio/ui/custom/avatar-card";
import ActivityReply from "./activity-reply";
import { useCreateReply } from '@relio/app/src/hooks/activities/use-create-reply';

const UserActivity = ({ activity, organization, isEdit, handleStateChange }: any) => {
	const [isReplying, setIsReplying] = useState(false);
	const [value, setValue] = useState('');

	const { mutate: createReply } = useCreateReply();

	const editor = useEditor({
		extensions: [
			StarterKit,
			Placeholder.configure({
				placeholder: "Reply...",
			}),
		],
		content: value,
		onUpdate: ({ editor }) => {
			setValue(editor.getHTML());
		},
	})

	const handleReply = async () => {
		try {
			await createReply({
				activityId: activity._id,
				orgId: organization?._id,
				message: value
			})

		} catch (e) {
			console.error("error creating reply", e)
		}

		setIsReplying(false);

		if (editor) {
			editor.commands.clearContent();
		}
	}

	return (
		<div className={'flex flex-col gap-2'}>
			<div className='flex flex-row justify-start w-full space-y-2'>
				<div className="flex flex-col justify-start space-y-2 mr-1 mt-2">
					{getActivityIcon(activity.type)}

					<div className="separator h-full border-r border-border mx-2.5 flex"></div>
				</div>
				<div className='flex flex-col w-full'>
					<div className='flex flex-row items-center space-x-3 mb-2'>
						<div className='text-sm flex flex-row items-center space-x-2 ml-2'>
							<AvatarCard record={activity?.user || []} />
							{activity?.user?.name}
						</div>
						<Tooltip>
							<TooltipTrigger asChild>
								<div className='text-xs text-gray-500'>
									{activity._creationTime && formatDistanceToNow(new Date(activity._creationTime), { addSuffix: true })}
									{activity.edited && <i> (edited)</i>}
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<div className='text-xs font-normal'>
									<div>
										{
											new Date(activity._creationTime).toLocaleString('en-US', {
												month: 'long',
												day: 'numeric',
												year: 'numeric',
												hour: 'numeric',
												minute: 'numeric',
												hour12: true
											})
										}
									</div>
								</div>
							</TooltipContent>
						</Tooltip>
					</div>
					<div className={cn(
						"row-item activity-wrapper !bg-muted/20 hover:!bg-muted/50",
						activity.resolved === true && 'opacity-30'
					)}>
						<div className='flex flex-start justify-between w-full'>
							<div
								dangerouslySetInnerHTML={{ __html: activity.message }}
								className='text-sm mt-1 font-normal text-foreground'
							/>
							<div className='item'>
								<ActivityEdit
									activity={activity}
									organization={organization}
									setIsReplying={setIsReplying}
									handleStateChange={handleStateChange}
									isEdit={isEdit}
								/>
							</div>
						</div>
						{activity.type === 'call' && (
							<div className='left-side w-full flex flex-col items-start !border-l-4 my-2'>
								<div className={'ml-2'}>
									{activity.result && (
										<div
											className='text-xs w-fit !py-0.25 !px-1 rounded-md !bg-green-900 border border-green-700'
										>
											{activity.result}
										</div>
									)}

									{activity.phone && (
										<div
											className='text-xs text-gray-500'
										>
											{activity.phone}
										</div>
									)}
								</div>
							</div>
						)}
					</div>
				</div>
			</div>

			
			{!activity.resolved && activity.replies && activity.replies.map((reply: any, index: number) => (
				<div key={index}>
					<ActivityReply activity={activity} reply={reply} organization={organization} />
				</div>
			)
			)}

			{activity.resolved && activity.replies && activity.replies.map((reply: any, index: number) => (
				<div key={index} className='text-xs text-gray-500 ml-10 hover:bg-muted/20 py-0.5 px-2 rounded-lg'>
					{activity.replies.length} Reply
				</div>
			)
			)}

			{isReplying && (
				<div className={cn('ml-10', isReplying && 'reply-box open')}>
					<div className='flex flex-row'>
						<div className={'flex flex-row items-center justify-between w-full p-2'}>
							<div>
								<AvatarCard record={activity.user} />
							</div>
							<EditorContent
								className="tiptap h-full w-full mt-2.5 ml-2.5"
								editor={editor}
							/>
							<Button
								disabled={value === "" || value.trim() === "" || value === "<p><br></p>"}
								className={cn('h-6 bg-indigo-700 hover:bg-indigo-900 text-white rounded-lg text-xs')}
								onClick={() => handleReply()}
							>
								Reply
							</Button>
						</div>

					</div>
				</div>
			)}
		</div>
	)
};

export default UserActivity