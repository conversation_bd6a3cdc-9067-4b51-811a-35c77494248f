"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import GridItem from '@relio/ui/custom/grid-item';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@relio/ui/dropdown-menu";
import { Button } from '@relio/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from "@relio/ui/avatar";
import { AddIcon, EllipsisIcon, Icons } from "@relio/ui/icons";
import LinkContactModal from "@relio/ui/modals/link-contact-modal";
import {Id} from "@relio/backend/convex/_generated/dataModel";
import { handleCopyId } from "@relio/ui/utils";
import { Organization } from '@relio/types';
import { useGetLinkedContacts } from '@relio/app/src/hooks/contacts/use-get-linked-contacts';
import { useUnlinkContact } from '@relio/app/src/hooks/contacts/use-unlink-contact';

type LinkedContactsProps = {
  data: any;
  organization: Organization;
  state: any;
  setState: any;
};

const LinkedContacts = ({ data, organization, state, setState }: LinkedContactsProps) => {
  const router = useRouter();
  const { data: linkedContacts } = useGetLinkedContacts(data?._id);
  const [selectedContact, setSelectedContact] = React.useState<any | null>(null);
  const { mutate: unlinkContact } = useUnlinkContact();

  return (
      <div className='flex flex-col gap-2'>
        {
          linkedContacts &&
          linkedContacts?.length > 0 ? (
              linkedContacts.map((contact, index) => {
                const c = contact.contact;
                return (
                    <div
                        key={index}
                        className="p-2 hover:bg-muted/30 border-none rounded-xl flex flex-row items-center justify-between gap-2"
                    >
                      <div
                          onClick={() => {
                            router.push(`${c?._id}`);
                          }}
                          className='flex flex-row items-center gap-2 cursor-pointer'
                      >
                        {/* Logo */}
                        <div className='rounded-xl border border-border bg-muted'>
                          <Avatar className='rounded-lg w-8 h-8'>
                            <AvatarImage src={c?.image || ""} alt={c?.fullName || ""}/>
                            <AvatarFallback>{(c?.firstName?.charAt(0) || "") + (c?.lastName?.charAt(0) || "")}</AvatarFallback>
                          </Avatar>
                        </div>

                        <div className='flex flex-col'>
                          <span className='text-sm font-medium'>{c?.fullName}</span>

                          <div className='flex flex-row items-center gap-2'>
                            <span className={'text-xs text-gray-500'}>{c?.email[0]?.address}</span>
                          </div>
                        </div>
                      </div>

                      <div className='flex flex-row items-center gap-2 pr-1'>
                  <span
                      className='bg-accent/50 border-accent text-xs text-gray-500 border rounded-lg px-1.5 py-0.5 flex flex-row items-center'>
                    {contact.relation}
                  </span>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-5 w-5 p-0">
                              <span className="sr-only">Open menu</span>
                              <EllipsisIcon className='h-3 w-3 text-muted-foreground'/>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className='rounded-xl'>
                            <DropdownMenuItem
                                className='rounded-lg'
                                onClick={() => handleCopyId(c?._id, 'Contact')}
                            >
                              Copy ID
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                className='rounded-lg'
                                onClick={() => {
                                  setState('openLinkedContactModal', true)
                                  setState('isEdit', true)
                                  setSelectedContact(contact)
                                }}
                            >
                              Change relation
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                className='rounded-lg !bg-destructive hover:!bg-destructive-foreground dark:hover:!text-black'
                                onClick={() => {
                                  unlinkContact({
                                    contact1: data._id,
                                    contact2: c?._id as Id<"contacts">,
                                    orgId: organization?._id
                                  }).then();
                                }}
                            >
                              Unlink
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                )
              })
          ) : (
              <GridItem
                  value={'Link a Contact'}
                  onClick={() => setState('openLinkedContactModal', true)}
                  icon={<AddIcon className='opacity-60'/>}
              />
          )
        }

        <LinkContactModal
            data={data}
            organization={organization}
            open={state.openLinkedContactModal}
            setOpen={(value: boolean) => setState('openLinkedContactModal', value)}
            isEdit={state.isEdit}
            currentData={selectedContact}
        />
      </div>
  );
}

export default LinkedContacts;
