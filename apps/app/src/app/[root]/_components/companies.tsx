import Link from "next/link";
import {DetailInput} from "@relio/ui/input";
import { Button, ButtonWithIcon } from "@relio/ui/button";
import { useGetContactCompany } from "@relio/app/src/hooks/contacts/use-get-contact-company";
import Image from "next/image";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@relio/ui/dropdown-menu";
import { AddIcon, EllipsisIcon, ListIcon, UnlinkIcon } from "@relio/ui/icons";
import { useGlobalState } from "@relio/app/src/providers/StateProvider";
import type { Id } from "@relio/backend/convex/_generated/dataModel";
import EmptyContainer from "@relio/ui/custom/empty-container";
import LinkCompanyModal from "@relio/ui/modals/link-company-modal";
import { useCurrentOrganization } from "@relio/app/src/hooks/organization/use-current-organization";
import { useUnlinkCompany } from "@relio/app/src/hooks/companies/use-unlink-company";
import { toast } from "sonner";
import { RBadge } from "@relio/ui/badge";

type Company = {
  name: string;
  domains: string[];
  image: string;
  _id: Id<"companies">;
  summary: string;
}

const Companies = ({ data }: { data: any }) => {
    const { state, setState } = useGlobalState();
    const { data: organization } = useCurrentOrganization();
    const { data: company } = useGetContactCompany({ id: data?._id }) as { data: Company | null, isLoading: boolean };
    const { mutate: unlinkCompany } = useUnlinkCompany();

    if (!data) {
      return;
    }

    const domains = company?.domains || [];

    return (
        <div className="px-2">
          <div className="flex flex-row items-center justify-between">
          <span className='text-lg font-medium'>Company</span>
          <ButtonWithIcon icon="add" onClick={() => {
            setState("openLinkedCompanyModal", true)
            setState("isEdit", false)
          }}>
            {!company ? "Add Company" : "Update Company"}
            </ButtonWithIcon>
          </div>
          {!company ? (
            <div>
              <EmptyContainer
                className="border-none"
                title="No company found"
                subtitle="There is no company associated with this contact."
                button="Add company"
                onClick={() => {
                  setState("isEdit", false)
                  setState("openLinkedCompanyModal", true)
                }}
                icon={AddIcon}
              />
            </div>
          ) : (
            <div className="border border-zinc-800 rounded-lg p-2 w-full mt-2">
              <div className="flex flex-row gap-2 mb-2 w-full">
                <Image src={company.image || ''} alt={"test"} width={50} height={50} className="rounded-lg" />

                  <div className="flex flex-col w-full">
                    <Link href={`/${organization?.slug}/companies/${company._id}`}>
                      <span className="text-zinc-200 text-md w-fit">
                        {company.name}
                      </span>
                    </Link>
                    <RBadge className="!w-fit">
                      {domains[0] || 'No domain'}
                    </RBadge>
                  </div>

                  <div className="flex flex-row gap-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="!h-5 !w-5 p-0">
                          <span className="sr-only">Open menu</span>
                          <EllipsisIcon className='h-3 w-3 text-muted-foreground'/>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className='rounded-xl space-y-1'>
                        <DropdownMenuItem
                            className='rounded-lg'
                        >
                          <ListIcon className="h-3 w-3 mr-2" />
                          Add to list
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            className='rounded-lg !bg-destructive hover:!bg-red-800'
                            onClick={() => {
                              unlinkCompany({
                                contactId: data._id,
                                companyId: company?._id as Id<"companies">,
                              }, {
                                onSuccess: (data) => {
                                  toast.success(`Company unlinked successfully"`)
                                },
                                onError: (error) => {
                                  toast.error("Error unlinking company")
                                },
                                onFinally: () => {
                                  console.log("Finally")
                                }
                              })
                            }}
                        >
                          <UnlinkIcon className="h-3 w-3 mr-2" />
                          Unlink
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                <DetailInput
                  type="text"
                  value={company.summary}
                  placeholder="Add title"
                  onChange={(e) => {}}
                  className="text-zinc-400 text-sm pl-2"
                  // onBlur={() => updateContact({
                  //   id: data._id,
                  //   orgId: orgId,
                  //   title: field.value,
                  // })}
                />
              </div>
          )}
          <LinkCompanyModal
            data={data}
            open={state.openLinkedCompanyModal}
            setOpen={(value: boolean) => setState('openLinkedCompanyModal', value)}
            orgId={organization?._id as Id<"organization">}
            isEdit={state.isEdit}
            currentData={state.currentData}
          />
        </div>
    );
};

export default Companies;