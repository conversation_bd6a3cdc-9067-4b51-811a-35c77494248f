"use client"

import * as React from "react"
import { NavMain } from "./nav-main"
import { NavFavorites } from "./nav-favorites"
import {
  Sidebar,
  SidebarContent,
  <PERSON>barFooter,
  SidebarHeader,
  SidebarRail,
  SidebarSeparator,
} from "@relio/ui/sidebar"
import { useState } from 'react';
import {useAction} from "convex/react";
import {api} from "@relio/backend/convex/_generated/api";
import {OrganizationSelector} from "@relio/ui/custom/organization-selector";
import QuickSearch from "@relio/ui/custom/quick-search";
import { usePathname } from "next/navigation";
import { useGetContacts } from '@relio/app/src/hooks/contacts/use-get-contacts';
import { useGetProperties } from '@relio/app/src/hooks/properties/use-get-properties';
import { useCurrentOrganization } from '@relio/app/src/hooks/organization/use-current-organization';
import { useGetLists } from '@relio/app/src/hooks/lists/use-get-lists';
import { useCreateCustomerPortal } from '@relio/app/src/hooks/use-create-customer-portal';
import { useCheck } from '@relio/app/src/hooks/use-check';
import { useGetCompanies } from '@relio/app/src/hooks/companies/use-get-companies';
import { recordItems, accountItems, organizationItems, dataItems } from '@relio/ui/constants/navigation';
import { NavRecords } from './nav-records';
import { SettingsHeader } from "@relio/ui/settings/settings-header"
import { SettingsNavAccount } from "./settings-nav-account"
import { SettingsNavOrganization } from "./settings-nav-organization"
import { SettingsNavData } from "./settings-nav-data"
import { NavLists } from './nav-lists';
import { useGetAllFavorites } from "@/hooks/favorites/use-get-all-favorites"

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();
  const { data: lists } = useGetLists();
  const { isProTrial } = useCheck();
  // const { data: pipelines } = useGetPipelines();

  const { data: organization } = useCurrentOrganization();
  const createCustomerPortal = useCreateCustomerPortal(organization?._id);
  const [status, setStatus] = useState(false);
  const embedAll = useAction(api.ingest.embed.embedAllTemp);
  // const { data: contacts } = useGetContacts({ 
  //   favorites: true,
  //   numItems: 10
  // })
  
  // const { data: properties } = useGetProperties({ 
  //   favorites: true,
  //   numItems: 10
  // })
  // const { data: companies } = useGetCompanies({ favorites: true })

  // const favorites = [
  //   ...(contacts || []),
  //   ...(properties || []),
  //   ...(companies || [])
  // ];

  const { favorites } = useGetAllFavorites();

  const handleCreateCustomerPortal = async () => {
    if (!organization?.customerId) {
      return;
    }
    const customerPortalUrl = await createCustomerPortal(); 

    if (!customerPortalUrl) {
      return;
    }

    window.location.href = customerPortalUrl;
  };

  return (
    <>
      {pathname.includes('settings') ? (
        <Sidebar collapsible="icon" {...props}>
          <SidebarHeader className="p-0">
            <SettingsHeader />
          </SidebarHeader>
          <SidebarContent>
            <SettingsNavAccount items={accountItems} slug={organization?.slug ?? ''} />
            <SettingsNavOrganization items={organizationItems} slug={organization?.slug ?? ''} />
            <SettingsNavData items={dataItems} slug={organization?.slug ?? ''} />
          </SidebarContent>
          <SidebarRail />
        </Sidebar>
      ) : (
        <Sidebar collapsible="icon" {...props}>
          <SidebarHeader className="p-0">
            <OrganizationSelector />
          </SidebarHeader>
          <SidebarContent>
            <QuickSearch />
            <NavMain slug={organization?.slug ?? ''} />
            <SidebarSeparator className="group-data-[collapsible=icon]:block group-data-[collapsible=icon]:mb-8 hidden" />
            <NavFavorites items={favorites || []} slug={organization?.slug ?? ''} />
            <NavRecords slug={organization?.slug ?? ''} />
            <NavLists items={lists || []} slug={organization?.slug ?? ''} />
          </SidebarContent>
          <SidebarFooter>
            {/* <NavUser user={data.user} /> */}
          </SidebarFooter>
          <SidebarRail />
        </Sidebar>
      )}
    </>
  )
}
