import { useGlobalState } from "@relio/app/src/providers/StateProvider";
import GridItem from "@relio/ui/custom/grid-item";
import { AddIcon, Icons } from "@relio/ui/icons";
import TaskModal from "@relio/ui/modals/task-modal";
import React, { useMemo, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@relio/ui/avatar";
import { format } from "date-fns";
import getTaskIcon from "@relio/ui/custom/get-task-icon";
import getPriorityIcon from "@relio/ui/custom/get-priority-icon";
import { useGetTasks } from "@relio/app/src/hooks/tasks/use-get-tasks";

const Tasks = ({ data }: any) => {
  const { setState } = useGlobalState();
  const { data: tasks } = useGetTasks();
  const [showCompletedTasks, setShowCompletedTasks] = useState(false);

  const filteredTasks = useMemo(() => {
    return tasks?.filter((task: any) => task?.linkedRecord?._id === data?._id) || [];
  }, [tasks, data?._id]);

  const activeTasks = filteredTasks.filter((task: any) => task.column !== "done");
  const completedTasks = filteredTasks.filter((task: any) => task.column === "done");

  const renderTask = (task: any) => (
    <div
      key={task._id}
      className="p-2 hover:bg-muted/30 border-none rounded-xl flex flex-row items-center justify-between gap-2"
    >
      <div
        onClick={() => {
          setState("isEdit", true);
          setState("openTaskModal", true);
          setState("data", task);
        }}
        className="flex flex-row items-center gap-2 cursor-pointer w-full"
      >
        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-row justify-between items-center">
            <div className="flex flex-row items-center gap-2">
              {getTaskIcon(task?.column as string)}
              <span className="text-sm font-medium truncate">{task.title}</span>
            </div>

            <Avatar className={"h-5 w-5"}>
              <AvatarImage
                src={task?.assignedTo?.photo || task?.assignedTo?.image}
                alt={task?.assignedTo?.name}
              />
              <AvatarFallback>{task?.assignedTo?.name?.charAt(0)}</AvatarFallback>
            </Avatar>
          </div>

          <div className="flex flex-row items-center gap-2">
            {getPriorityIcon(task?.priority as string)}
            <span className={"text-xs text-gray-500"}>
              {task?.dueDate ? format(new Date(task.dueDate), 'MMM d h:mm a') : ""}
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col gap-2">
      <TaskModal currentRecord={true} />
      {activeTasks.length > 0 ? (
        activeTasks.map(renderTask)
      ) : (
        <GridItem
          value={"Add Task"}
          onClick={() => {
            setState("openTaskModal", true);
            setState("isEdit", false);
            setState("data", data);
          }}
          icon={<AddIcon className="opacity-60" />}
        />
      )}
      
      {completedTasks.length > 0 && (
        <div className="mt-2">
          <div
            onClick={() => setShowCompletedTasks(!showCompletedTasks)}
            className="w-fit text-xs flex justify-start items-center gap-2 hover:underline cursor-pointer dark:text-zinc-500 dark:hover:text-zinc-200"
          >
            {showCompletedTasks ? "Hide completed tasks" : `View completed tasks (${completedTasks.length})`}
          </div>
          {showCompletedTasks && completedTasks.map(renderTask)}
        </div>
      )}
    </div>
  );
};

export default Tasks;
