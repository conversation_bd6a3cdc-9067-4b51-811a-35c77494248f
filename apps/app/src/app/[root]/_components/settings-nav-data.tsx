"use client"

import Link from "next/link"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@relio/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@relio/ui/collapsible"
import { cn } from "@relio/ui/utils";
import { getIcon, ChevronDownIcon, DataIcon } from "@relio/ui/icons";
import { NavItem } from "@relio/types";
import { usePathname } from "next/navigation";

const addSlugToNavItems = (navItems: NavItem[], slug: string): NavItem[] => {
  return navItems.map((item) => ({
    ...item,
    href: `/${slug}${item.href}`,
  }));
};

export function SettingsNavData({
  items,
  slug
}: {
  items: NavItem[];
  slug: string;
}) {
  const pathname = usePathname();
  const itemsWithSlug = addSlugToNavItems(items, slug);

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <Collapsible defaultOpen className="group/collapsible group-data-[collapsible=icon]:mb-6 group-data-[collapsible=icon]:mt-8">
          <SidebarGroup>
            <SidebarGroupLabel className="group-data-[collapsible=icon]:opacity-100 !p-0">
              <CollapsibleTrigger className={cn(
                  "!h-6",
                  "peer/menu-button flex w-full items-center overflow-hidden rounded-md text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",
                  "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              )}>
                <div className="group-data-[collapsible=icon]:block hidden">
                  <DataIcon />
                </div>
                <div className="flex items-center gap-2 group-data-[collapsible=icon]:hidden px-2">
                  <ChevronDownIcon className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
                  <span className="text-xs">
                    Data
                  </span>
                </div>
              </CollapsibleTrigger>
            </SidebarGroupLabel>
            <CollapsibleContent>
              <SidebarMenu>
                {itemsWithSlug.map((item: any, index: number) => (
                  <SidebarMenuItem key={index}>
                    <SidebarMenuButton 
                      tooltip={item.title}
                      className="group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:items-center"
                      asChild
                    >
                      <Link
                        href={item.disabled ? "/" : item.href} 
                      >
                        {getIcon(item.icon, { className: `size-4` })}
                        <span className="group-data-[collapsible=icon]:hidden">
                          {item.title}
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </CollapsibleContent>
          </SidebarGroup>
        </Collapsible>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
