import React, { useState } from 'react';
import { Textarea } from "@relio/ui/textarea"
import {Id} from "@relio/backend/convex/_generated/dataModel";
import { Organization } from '@relio/types';
import { useUpdateContact } from '@relio/app/src/hooks/contacts/use-update-contact';

type SummaryProps = {
  data: any;
  recordType: string;
  organization: Organization;
}

const Summary = ({ data, recordType, organization }: SummaryProps) => {
  const [note, setNote] = useState(data?.summary || '');
  const { mutate: updateContact } = useUpdateContact();

  const handleNoteChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNote(e.target.value);
  };

  const handleUpdateNote = () => {
    if (note !== data?.summary) {
      updateContact({
        id: data._id,
        summary: note,
        orgId: organization?._id as Id<"organization">
      }).then();
    }
  }

  return (
    <Textarea
      className="w-full h-40 p-2 rounded-none border-none shadow-none focus-visible:ring-0 focus-visible:outline-none bg-zinc-200 dark:!secondary-bg"
      placeholder="Summary..."
      value={note || data?.summary}
      onChange={handleNoteChange}
      onBlur={handleUpdateNote}
      style={{ resize: 'none' }}
    />
  );
}

export default Summary;
