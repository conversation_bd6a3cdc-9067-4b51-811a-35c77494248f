"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
  useSidebar,
} from "@relio/ui/sidebar"
import { Avatar, AvatarFallback, AvatarImage } from "@relio/ui/avatar";
import { useToggleFavorite } from '@relio/app/src/hooks/favorites/use-toggle-favorite';
import { ChevronDownIcon, ListIcon, StarIcon as StarFilled, StarOffIcon as StarOff } from "@relio/ui/icons";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@relio/ui/collapsible"
import { cn } from "@relio/ui/utils";
import getRecordIcon from "@relio/ui/custom/get-record-icon";
import { RecordsIcon } from "@relio/ui/icons";
import { NavItem } from "@relio/types";
import { Id } from "@relio/backend/convex/_generated/dataModel";

export function NavLists({
  items,
  slug
}: {
  items: {
    _id: Id<"lists">;
    recordType: "contacts" | "properties" | "companies";
    name?: string | undefined;
  }[]
  slug: string;
}) {
  const { isMobile } = useSidebar();
  const { toggleFavorite } = useToggleFavorite();
  const [isOpen, setIsOpen] = useState(false)

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <Collapsible defaultOpen className="group/collapsible">
          <SidebarGroup>
            <SidebarGroupLabel className="group-data-[collapsible=icon]:opacity-100 !p-0">
              <CollapsibleTrigger className={cn(
                  "!h-6",
                  "peer/menu-button flex w-full items-center overflow-hidden rounded-md text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",
                  "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              )}>
                <div className="group-data-[collapsible=icon]:block hidden">
                  <ListIcon />
                </div>
                <div className="flex items-center gap-2 group-data-[collapsible=icon]:hidden px-2">
                  <ChevronDownIcon className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
                  <span className="text-xs">
                    Lists
                  </span>
                </div>
              </CollapsibleTrigger>
            </SidebarGroupLabel>
            <CollapsibleContent>
              <SidebarMenu>
              {items.length < 1 && (
                  <span className={cn(
                    "text-xs cursor-default text-foreground/50 pl-3",
                    "group-data-[collapsible=icon]:hidden"
                  )}>
                    No lists yet
                  </span>
                )}

                {items.length < 1 && (
                  <div className="w-full justify-center items-center hidden group-data-[collapsible=icon]:flex">
                    <span className={cn(
                      "text-xs cursor-default text-foreground/50",
                      "group-data-[collapsible=icon]:block"
                    )}>
                      None
                    </span>
                  </div>
                )}

                {items.map((item, index: number) => (
                  <SidebarMenuItem key={index}>
                    <SidebarMenuButton 
                      className="group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:items-center"
                      asChild
                    >
                      <Link
                        href={`/${slug}/${item.recordType}/list/${item._id}`}
                      >
                        {getRecordIcon(item.recordType as string)}
                        <span className="group-data-[collapsible=icon]:hidden">
                          {item.name}
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </CollapsibleContent>
          </SidebarGroup>
        </Collapsible>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
