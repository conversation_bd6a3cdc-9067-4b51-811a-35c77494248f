import { cn } from "@relio/ui/utils";
import { Tooltip, TooltipContent, TooltipTrigger } from "@relio/ui/tooltip";
import { format, formatDistanceToNow } from "date-fns";
import React from "react";
import AvatarCard from "@relio/ui/custom/avatar-card";
import { useCurrentUser } from "@relio/app/src/hooks/user/use-current-user";

const ActivityReply = ({ activity, reply, organization }: any) => {
	console.log("activity", activity);

	return (
		<div className={'flex flex-col gap-2 ml-10'}>
			<div className={cn(
				"row-item flex flex-row items-center hover:bg-zinc-100 dark:hover:bg-muted/20 w-full p-2 rounded-xl",
				activity?.resolved === true && 'opacity-30'
			)}>
				<div className={'flex flex-col gap-1 w-full'}>

					{/* User */}
					<div className="flex flex-row justify-between">
						<div className={'flex flex-row items-center gap-2'}>
							<div className={'flex flex-col'}>
								<AvatarCard record={reply?.user} />
								<div className="separator h-full border-r border-border"></div>
							</div>
							<div className='text-sm font-semibold -ml-2'>
								{reply?.user?.name}
							</div>
							<Tooltip>
								<TooltipTrigger asChild>
									<div className='text-xs font-normal text-muted-foreground'>
										Replied {reply?._creationTime && formatDistanceToNow(new Date(reply?._creationTime), { addSuffix: true })}
									</div>
								</TooltipTrigger>
								<TooltipContent>
									<div className='text-xs font-normal'>
										{format(new Date(reply._creationTime), 'PPpp')}
									</div>
								</TooltipContent>
							</Tooltip>
						</div>

						{/* Actions */}
						{/*{reply?.createdBy._id === currentUser._id && (*/}
						{/*    <ReplyActions*/}
						{/*        reply={reply}*/}
						{/*        currentUser={currentUser}*/}
						{/*        activity={activity}*/}
						{/*        replies={replies}*/}
						{/*        setReplies={setReplies}*/}
						{/*    />*/}
						{/*)}*/}
					</div>

					<div
						dangerouslySetInnerHTML={{ __html: reply.message }}
						className='ml-8 text-sm font-normal text-foreground'
					/>
				</div>

			</div>
		</div>
	)
}

export default ActivityReply;