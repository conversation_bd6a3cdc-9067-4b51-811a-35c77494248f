"use client"

import React, { useState, useCallback, useEffect } from 'react'
import Grid from "@relio/ui/grid";
import { contactColumnDefs } from "@relio/ui/grid/columns";
import { Id } from "@relio/backend/convex/_generated/dataModel";
import { useCurrentUser } from '@relio/app/src/hooks/user/use-current-user';
import { useCurrentOrganization } from '@relio/app/src/hooks/organization/use-current-organization';
import { useGetContacts } from '@relio/app/src/hooks/contacts/use-get-contacts';
import Loader from "@relio/ui/custom/loader";
import { toast } from "sonner";
import type { PaginationParams, PaginationResult } from "@relio/ui/types/data-table";
import type { FilterCondition } from "@relio/ui/filters/data-filter";

// Define the missing types
interface EmailItem {
	label?: string;
	address?: string;
	isBad?: boolean;
	isPrimary?: boolean;
}

interface PhoneItem {
	label?: string;
	number?: string;
	isBad?: boolean;
	isPrimary?: boolean;
}

interface Contact {
	_id: Id<"contacts">;
	fullName: string;
	email?: EmailItem[];
	phone?: PhoneItem[];
	status?: string;
	persona?: string;
	stage?: string;
	[key: string]: any;
}

// Debug function to help diagnose filter issues
function debugContactData(contacts: Contact[], filters: FilterCondition[], totalCount: number) {
	if (!contacts.length) return;

	// Log the first 3 contacts with more detailed email structure
	console.log('Debug: First 3 contacts structure:', contacts.slice(0, 3).map(contact => ({
		fullName: contact.fullName,
		email: contact.email ? contact.email.map((e: EmailItem) => ({
			label: e.label,
			address: e.address,
			isBad: e.isBad,
			isPrimary: e.isPrimary
		})) : [],
		phone: contact.phone ? contact.phone.map((p: PhoneItem) => ({
			label: p.label,
			number: p.number,
			isBad: p.isBad,
			isPrimary: p.isPrimary
		})) : [],
		status: contact.status,
		persona: contact.persona,
		stage: contact.stage
	})));

	// For email and phone filters, check if the data structure matches what we expect
	const emailFilters = filters.filter(f => f.field === 'email');
	const phoneFilters = filters.filter(f => f.field === 'phone');
	const fullNameFilters = filters.filter(f => f.field === 'fullName');

	if (emailFilters.length) {
		console.log('Debug: Email filter applied:', emailFilters);

		// Count contacts with non-empty email addresses
		const contactsWithEmails = contacts.filter(c =>
			c.email &&
			c.email.length > 0 &&
			c.email.some((e: EmailItem) => e.address && e.address.trim() !== '')
		);

		console.log(`Debug: ${contactsWithEmails.length} out of ${contacts.length} contacts have non-empty email addresses`);
		console.log(`Debug: Total filtered count from server: ${totalCount}`);

		// Check for contacts with email addresses containing the filter value
		if (emailFilters[0]) {
			const filterValue = emailFilters[0].value.toString().toLowerCase();
			const matchingContacts = contacts.filter(c =>
				c.email &&
				c.email.length > 0 &&
				c.email.some((e: EmailItem) => e.address && e.address.toLowerCase().includes(filterValue))
			);

			console.log(`Debug: Found ${matchingContacts.length} contacts with email addresses containing "${filterValue}" in current page`);

			if (matchingContacts.length > 0) {
				console.log('Debug: First matching contact:', {
					fullName: matchingContacts[0].fullName,
					emails: matchingContacts[0].email
				});
			}

			// Check if the filter is working correctly
			if (emailFilters[0].operator === 'contains' && matchingContacts.length === 0 && contactsWithEmails.length > 0) {
				console.warn('Debug: Filter may not be working correctly - no matches found despite having contacts with emails');
			}
		}

		// Sample of email structures
		const emailSamples = contacts
			.filter(c => c.email && c.email.length)
			.slice(0, 5)
			.flatMap(c => c.email);

		console.log('Debug: Sample email structures:', emailSamples);

		// Check if any contacts have empty email addresses
		const contactsWithEmptyEmails = contacts.filter(c =>
			c.email &&
			c.email.length > 0 &&
			c.email.every((e: EmailItem) => !e.address || e.address.trim() === '')
		);

		console.log(`Debug: ${contactsWithEmptyEmails.length} contacts have only empty email addresses`);


	}

	if (phoneFilters.length) {
		console.log('Debug: Phone filter applied:', phoneFilters);

		// Count contacts with non-empty phone numbers
		const contactsWithPhones = contacts.filter(c =>
			c.phone &&
			c.phone.length > 0 &&
			c.phone.some((p: PhoneItem) => p.number && p.number.trim() !== '')
		);

		console.log(`Debug: ${contactsWithPhones.length} out of ${contacts.length} contacts have non-empty phone numbers`);

		// Check for contacts with phone numbers containing the filter value
		if (phoneFilters[0]) {
			const filterValue = phoneFilters[0].value.toString().toLowerCase();
			const matchingContacts = contacts.filter(c =>
				c.phone &&
				c.phone.length > 0 &&
				c.phone.some((p: PhoneItem) => p.number && p.number.toLowerCase().includes(filterValue))
			);

			console.log(`Debug: Found ${matchingContacts.length} contacts with phone numbers containing "${filterValue}" in current page`);

			if (matchingContacts.length > 0) {
				console.log('Debug: First matching contact:', {
					fullName: matchingContacts[0]?.fullName,
					phones: matchingContacts[0]?.phone
				});
			}
		}

		// Sample of phone structures
		const phoneSamples = contacts
			.filter(c => c.phone && c.phone.length)
			.slice(0, 5)
			.flatMap(c => c.phone);

		console.log('Debug: Sample phone structures:', phoneSamples);
	}

	if (fullNameFilters.length) {
		console.log('Debug: Full Name filter applied:', fullNameFilters);

		// Check for contacts with full names containing the filter value
		if (fullNameFilters[0]) {
			const filterValue = fullNameFilters[0].value.toString().toLowerCase();
			const matchingContacts = contacts.filter(c =>
				c.fullName && c.fullName.toLowerCase().includes(filterValue)
			);

			console.log(`Debug: Found ${matchingContacts.length} contacts with full names containing "${filterValue}" in current page`);

			if (matchingContacts.length > 0) {
				console.log('Debug: First matching contact:', {
					fullName: matchingContacts[0].fullName
				});
			}
		}
	}

	// Log other filter types
	const otherFilters = filters.filter(f =>
		f.field !== 'email' &&
		f.field !== 'phone' &&
		f.field !== 'fullName' &&
		f.field !== undefined
	);

	if (otherFilters.length) {
		console.log('Debug: Other filters applied:', otherFilters);

		// Check if the filters are working correctly
		otherFilters.forEach(filter => {
			const matchingContacts = contacts.filter(c => {
				const fieldValue = c[filter.field];
				if (fieldValue === undefined || fieldValue === null) return false;

				switch (filter.operator) {
					case 'contains':
						return typeof fieldValue === 'string' && fieldValue.toLowerCase().includes(filter.value.toString().toLowerCase());
					case 'equals':
						return fieldValue === filter.value;
					default:
						return false;
				}
			});

			console.log(`Debug: Found ${matchingContacts.length} contacts matching filter ${filter.field} ${filter.operator} ${filter.value}`);
		});
	}

	// Check for malformed filters
	const malformedFilters = filters.filter(f => f.field === undefined);
	if (malformedFilters.length) {
		console.error('Debug: Found malformed filters missing field property:', malformedFilters);
	}
}

const Contacts = () => {
	const { user } = useCurrentUser();
	const { data: organization } = useCurrentOrganization();
	const [pageSize, setPageSize] = useState(100);
	const [cursor, setCursor] = useState<string | null>(null);
	const [filters, setFilters] = useState<FilterCondition[]>([]);
	const [isFiltering, setIsFiltering] = useState(false);

	// Debug logging for filters
	useEffect(() => {
		console.log('Contacts page filters updated:', JSON.stringify(filters, null, 2));
		setIsFiltering(filters.length > 0);
	}, [filters]);

	// Get contacts for the current page
	const {
		data: contacts,
		isLoading,
		total,
		getPage
	} = useGetContacts({
		numItems: pageSize,
		cursor,
		filters
	});

	// Debug contact data when it changes or filters change
	useEffect(() => {
		if (contacts && contacts.length) {
			debugContactData(contacts, filters, total);
		}
	}, [contacts, filters, total]);

	const orgId = organization?._id as Id<"organization">
	const userId = user?._id as Id<"users">

	// Handle page size changes
	const handlePageSizeChange = (newSize: number) => {
		setPageSize(newSize);
		setCursor(null);
	};

	// Handle cursor changes
	const handleCursorChange = (newCursor: string | null) => {
		setCursor(newCursor);
	};

	// Handle filter changes
	const handleFilterChange = useCallback((newFilters: FilterCondition[]) => {
		console.log('Filter change handler called with:', JSON.stringify(newFilters, null, 2));

		// Ensure all filters have a field property
		const validatedFilters = newFilters.map(filter => {
			// If the filter is missing the field property, assume it's for fullName
			if (!filter.field) {
				console.warn('Received filter without field property, defaulting to fullName:', filter);
				return { ...filter, field: 'fullName' };
			}
			return filter;
		});

		setFilters(validatedFilters);
		setCursor(null);
	}, []);

	// Custom getPage function that handles pagination
	const handleGetPage = async (params: PaginationParams): Promise<PaginationResult<Contact>> => {
		try {
			const result = await getPage({
				...params,
				filters
			});

			return result;
		} catch (error) {
			console.error('Error fetching page:', error);
			toast.error('Failed to fetch contacts');
			return {
				success: false,
				rows: [],
				totalCount: 0
			};
		}
	};

	if (!orgId || !userId) {
		return (
			<div className="flex items-center justify-center h-screen">
				<Loader />
				<div className="ml-4">Loading organization and user data...</div>
			</div>
		);
	}

	if (isLoading && !cursor) {
		return (
			<div className="flex items-center justify-center h-screen">
				<Loader />
				<div className="ml-4">Loading contacts...</div>
			</div>
		);
	}

	return (
		<div>
			{isFiltering && (
				<div className="p-2 bg-blue-50 text-blue-800 border-b border-blue-200">
					Showing {total} filtered contacts
					<button
						className="ml-2 text-blue-600 hover:text-blue-800 underline"
						onClick={() => handleFilterChange([])}
					>
						Clear filters
					</button>
				</div>
			)}
			<Grid
				recordType="contacts"
				userId={userId}
				orgId={orgId}
				columnDefs={contactColumnDefs}
				data={contacts || []}
				totalRecords={total}
				isLoading={isLoading}
				getPage={handleGetPage}
				onPageSizeChange={handlePageSizeChange}
				onCursorChange={handleCursorChange}
				onFilterChange={handleFilterChange}
				pageSize={pageSize}
				cursor={cursor}
			/>
		</div>
	)
}

export default Contacts
