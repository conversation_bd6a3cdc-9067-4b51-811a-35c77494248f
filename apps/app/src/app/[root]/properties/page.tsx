"use client";

import React, { useState, useCallback } from "react";
import Grid from "@relio/ui/grid";
import { propertyColumnDefs } from "@relio/ui/grid/columns";
import { Id } from "@relio/backend/convex/_generated/dataModel";
import { useGetProperties } from '@relio/app/src/hooks/properties/use-get-properties';
import { useCurrentUser } from '@relio/app/src/hooks/user/use-current-user';
import { useCurrentOrganization } from '@relio/app/src/hooks/organization/use-current-organization';
// Define types locally to avoid import issues
interface PaginationParams {
  startRow: number;
  endRow: number;
  orgId: string;
  cursor?: string | null;
  filters?: FilterCondition[];
}

interface PaginationResult<T> {
  rows: T[];
  success: boolean;
  totalCount?: number;
  cursor?: string | null;
}
import type { FilterCondition } from "@relio/ui/filters/data-filter";
import { toast } from "sonner";
import Loader from "@relio/ui/custom/loader";

// Property type for type safety - use any for simplicity
type Property = any;

const Properties = () => {
  const { user } = useCurrentUser();
  const { data: organization } = useCurrentOrganization();

  // Pagination state
  const [pageSize, setPageSize] = useState(100);
  const [cursor, setCursor] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState<FilterCondition[]>([]);

  let orgId = organization?._id as Id<"organization">;
  let userId = user?._id as Id<"users">;

  // Use the properties hook with pagination and filters
  const {
    data: properties,
    isLoading,
    total,
    getPage
  } = useGetProperties({
    numItems: pageSize,
    cursor,
    filters: filters
  });

  // Handle page size changes
  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCursor(null);
    setCurrentPage(1);
  };

  // Handle cursor changes
  const handleCursorChange = (newCursor: string | null) => {
    setCursor(newCursor);
    // Note: currentPage will be managed by the Grid component internally
  };

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: FilterCondition[]) => {
    console.log('Filter change handler called with:', JSON.stringify(newFilters, null, 2));

    // Ensure all filters have a field property
    const validatedFilters = newFilters.map(filter => {
      // If the filter is missing the field property, assume it's for name
      if (!filter.field) {
        console.warn('Received filter without field property, defaulting to name:', filter);
        return { ...filter, field: 'name' };
      }
      return filter;
    });

    setFilters(validatedFilters);
    setCursor(null);
    setCurrentPage(1);
  }, []);

  // Custom getPage function that handles pagination
  const handleGetPage = async (params: PaginationParams): Promise<PaginationResult<Property>> => {
    try {
      const result = await getPage({
        ...params,
        filters: filters
      });

      return result;
    } catch (error) {
      console.error('Error fetching page:', error);
      toast.error('Failed to fetch properties');
      return {
        success: false,
        rows: [],
        totalCount: 0
      };
    }
  };

  // Show loading state for initial load
  if (isLoading && !cursor) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader />
        <div className="ml-4">Loading properties...</div>
      </div>
    );
  }

  return (
    <div>
      <Grid
        recordType="properties"
        userId={userId}
        orgId={orgId!}
        columnDefs={propertyColumnDefs}
        data={properties ?? []}
        totalRecords={total}
        isLoading={isLoading}
        getPage={handleGetPage}
        onPageSizeChange={handlePageSizeChange}
        onCursorChange={handleCursorChange}
        onFilterChange={handleFilterChange}
        pageSize={pageSize}
        cursor={cursor}
        currentPage={currentPage}
      />
    </div>
  );
};

export default Properties;
