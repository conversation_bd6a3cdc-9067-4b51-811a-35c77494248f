import { api } from "@relio/backend/convex/_generated/api";
import type { Id } from "@relio/backend/convex/_generated/dataModel";
import { PaginationParams } from "@relio/ui/data-table/types";
import { useConvex, useQuery } from "convex/react";
import { useCallback, useMemo, useEffect } from "react";
import { useCurrentOrganization } from "../organization/use-current-organization";

import type { FilterCondition } from "@relio/ui/filters/data-filter";

export type UseGetPropertiesProps = {
  favorites?: boolean;
  query?: string;
  numItems?: number;
  cursor?: string | null;
  filters?: FilterCondition[];
  isDeleted?: boolean;
  sort?: { field: string; direction: 'asc' | 'desc' } | null;
};

export const useGetProperties = ({
  favorites = false,
  query,
  numItems = 100,
  cursor = null,
  filters = [],
  isDeleted = false,
  sort = null
}: UseGetPropertiesProps = {}) => {
  const { data: organization } = useCurrentOrganization();
  const orgId = organization?._id as Id<"organization">;
  const convex = useConvex();

  const filterKey = useMemo(() =>
    JSON.stringify({ filters, sort }),
    [filters, sort]
  );

  useEffect(() => {
    if (filters.length > 0) {
      console.log('useGetProperties with filters:', filters);
    }
  }, [filters]);

  const queryResult = useQuery(
    api.properties.getProperties,
    orgId ? {
      orgId: orgId as Id<"organization">,
      paginationOpts: { numItems, cursor },
      ...(query && { query }),
      ...(favorites && { favorites }),
      isDeleted,
      ...(filters.length > 0 && { filters }),
      ...(sort && sort.field && sort.direction ? { sort } : {})
    } : "skip"
  );

  const data = useMemo(() => queryResult?.page ?? [], [queryResult?.page]);
  const isLoading = queryResult === undefined;
  const total = queryResult?.totalCount ?? 0;

  const getPage = useCallback(async ({
    startRow = 0,
    endRow = 100,
    filters: pageFilters = [],
    sort: pageSort = null,
    orgId: paramOrgId,
    cursor: paramCursor
  }: PaginationParams) => {
    if (!paramOrgId) {
      return {
        success: false,
        rows: [],
        totalCount: 0
      };
    }

    try {
      const pageSize = endRow - startRow;
      const safePageSize = Math.max(pageSize, 10);

      const mergedFilters = [...filters, ...pageFilters];

      const effectiveSort = pageSort && typeof pageSort === 'object' && !Array.isArray(pageSort) &&
        pageSort.field && pageSort.direction ? pageSort :
        (sort && sort.field && sort.direction ? sort : null);

      const queryParams = {
        orgId: paramOrgId as Id<"organization">,
        paginationOpts: {
          numItems: safePageSize,
          cursor: paramCursor || null
        },
        query: query || undefined,
        favorites,
        isDeleted,
        ...(mergedFilters.length > 0 && { filters: mergedFilters }),
        ...(effectiveSort ? { sort: effectiveSort } : {})
      };

      const response = await convex.query(api.properties.getProperties, queryParams);

      return {
        success: true,
        rows: response?.page ?? [],
        totalCount: response?.totalCount ?? 0,
        cursor: response?.cursor
      };
    } catch (error) {
      console.error('Error fetching properties:', error);
      return {
        success: false,
        rows: [],
        totalCount: 0
      };
    }
  }, [convex, favorites, isDeleted, query, filters, sort]);

  return {
    data,
    isLoading,
    total,
    getPage
  };
};
