import { FilterCondition } from "../components/filters/data-filter";

// Helper function to determine if a field is an array field that needs special handling
function isArrayField(field: string): boolean {
  return field === 'email' || field === 'phone';
}

// Helper function to get the appropriate subfield for array fields
function getArraySubfield(field: string): string {
  if (field === 'email') return 'address';
  if (field === 'phone') return 'number';
  return '';
}

/**
 * Applies an array of filter conditions to a Convex query
 * Handles special cases for array fields like email and phone
 */
export function applyFiltersToQuery(
  query: any,
  filters: FilterCondition[]
): any {
  if (!filters.length) return query;

  console.log('Applying filters to query:', JSON.stringify(filters, null, 2));

  return query.filter((q) => {
    const conditions = filters.map((filter) => {
      // Handle array fields differently (email and phone are arrays of objects)
      if (isArrayField(filter.field)) {
        const subfield = getArraySubfield(filter.field);
        console.log(`Processing array filter: field=${filter.field}, subfield=${subfield}, operator=${filter.operator}, value=${filter.value}`);

        // For array fields, we need to check if any element in the array matches the condition
        switch (filter.operator) {
          case 'contains': {
            // First ensure the array exists and is not empty
            const arrayExists = q.and(
              q.neq(q.field(filter.field), null),
              q.neq(q.field(filter.field), [])
            );

            // Then check if any element in the array has a non-empty subfield that contains the value
            const hasMatchingElement = q.some(q.field(filter.field), (item) =>
              q.and(
                q.neq(item.field(subfield), null),
                q.neq(item.field(subfield), ""),
                q.includes(item.field(subfield), filter.value.toString())
              )
            );

            return q.and(arrayExists, hasMatchingElement);
          }
          case 'notContains': {
            // First ensure the array exists and is not empty
            const arrayExists = q.and(
              q.neq(q.field(filter.field), null),
              q.neq(q.field(filter.field), [])
            );

            // Then check that no element in the array has a non-empty subfield that contains the value
            const noMatchingElement = q.not(
              q.some(q.field(filter.field), (item) =>
                q.and(
                  q.neq(item.field(subfield), null),
                  q.neq(item.field(subfield), ""),
                  q.includes(item.field(subfield), filter.value.toString())
                )
              )
            );

            return q.and(arrayExists, noMatchingElement);
          }
          case 'equals': {
            // Check if any element in the array has a subfield equal to the value
            return q.some(q.field(filter.field), (item) =>
              q.and(
                q.neq(item.field(subfield), null),
                q.neq(item.field(subfield), ""),
                q.eq(item.field(subfield), filter.value)
              )
            );
          }
          case 'notEquals': {
            // First ensure the array exists and is not empty
            const arrayExists = q.and(
              q.neq(q.field(filter.field), null),
              q.neq(q.field(filter.field), [])
            );

            // Then check that no element in the array has a subfield equal to the value
            const noMatchingElement = q.not(
              q.some(q.field(filter.field), (item) =>
                q.and(
                  q.neq(item.field(subfield), null),
                  q.neq(item.field(subfield), ""),
                  q.eq(item.field(subfield), filter.value)
                )
              )
            );

            return q.and(arrayExists, noMatchingElement);
          }
          case 'startsWith': {
            return q.some(q.field(filter.field), (item) =>
              q.and(
                q.neq(item.field(subfield), null),
                q.neq(item.field(subfield), ""),
                q.regexMatch(item.field(subfield), `^${filter.value}`)
              )
            );
          }
          case 'endsWith': {
            return q.some(q.field(filter.field), (item) =>
              q.and(
                q.neq(item.field(subfield), null),
                q.neq(item.field(subfield), ""),
                q.regexMatch(item.field(subfield), `${filter.value}$`)
              )
            );
          }
          case 'isEmpty': {
            // Check if the array is empty or all elements have empty subfields
            return q.or(
              q.eq(q.field(filter.field), null),
              q.eq(q.field(filter.field), []),
              q.every(q.field(filter.field), (item) =>
                q.or(
                  q.eq(item.field(subfield), null),
                  q.eq(item.field(subfield), "")
                )
              )
            );
          }
          case 'isNotEmpty': {
            // Check if the array exists, is not empty, and at least one element has a non-empty subfield
            return q.and(
              q.neq(q.field(filter.field), null),
              q.neq(q.field(filter.field), []),
              q.some(q.field(filter.field), (item) =>
                q.and(
                  q.neq(item.field(subfield), null),
                  q.neq(item.field(subfield), "")
                )
              )
            );
          }
          default:
            console.warn(`Unknown operator for array field: ${filter.operator}`);
            return q.eq(1, 1); // No-op condition
        }
      } else {
        // Handle regular fields
        const fieldRef = q.field(filter.field);
        console.log(`Processing filter: field=${filter.field}, operator=${filter.operator}, value=${filter.value}`);

        switch (filter.operator) {
          case 'contains':
            return q.and(
              q.neq(fieldRef, null),
              q.neq(fieldRef, ""),
              q.includes(fieldRef, filter.value.toString())
            );
          case 'notContains':
            return q.or(
              q.eq(fieldRef, null),
              q.eq(fieldRef, ""),
              q.not(q.includes(fieldRef, filter.value.toString()))
            );
          case 'equals':
            return q.eq(fieldRef, filter.value);
          case 'notEquals':
            return q.neq(fieldRef, filter.value);
          case 'startsWith':
            return q.and(
              q.neq(fieldRef, null),
              q.neq(fieldRef, ""),
              q.regexMatch(fieldRef, `^${filter.value}`)
            );
          case 'endsWith':
            return q.and(
              q.neq(fieldRef, null),
              q.neq(fieldRef, ""),
              q.regexMatch(fieldRef, `${filter.value}$`)
            );
          case 'greaterThan':
            return q.gt(fieldRef, Number(filter.value));
          case 'lessThan':
            return q.lt(fieldRef, Number(filter.value));
          case 'greaterOrEqual':
            return q.gte(fieldRef, Number(filter.value));
          case 'lessOrEqual':
            return q.lte(fieldRef, Number(filter.value));
          case 'isEmpty':
            return q.or(
              q.eq(fieldRef, null),
              q.eq(fieldRef, "")
            );
          case 'isNotEmpty':
            return q.and(
              q.neq(fieldRef, null),
              q.neq(fieldRef, "")
            );
          case 'isTrue':
            return q.eq(fieldRef, true);
          case 'isFalse':
            return q.eq(fieldRef, false);
          default:
            console.warn(`Unknown operator: ${filter.operator}`);
            return q.eq(1, 1); // No-op condition
        }
      }
    });

    return q.and(...conditions);
  });
}