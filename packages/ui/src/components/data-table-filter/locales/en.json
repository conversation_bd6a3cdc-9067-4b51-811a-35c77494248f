{"clear": "Clear", "search": "Search...", "noresults": "No results.", "operators": "Operators", "filter": "Filter", "and": "and", "single": "Single", "range": "Range", "value": "Value", "min": "Min", "max": "Max", "filters.option.is": "is", "filters.option.isNot": "is not", "filters.option.isAnyOf": "is any of", "filters.option.isNoneOf": "is none of", "filters.multiOption.include": "includes", "filters.multiOption.exclude": "excludes", "filters.multiOption.includeAnyOf": "includes any of", "filters.multiOption.excludeAllOf": "excludes all of", "filters.multiOption.includeAllOf": "includes all of", "filters.multiOption.excludeIfAnyOf": "excludes if any of", "filters.multiOption.excludeIfAll": "excludes if all of", "filters.date.is": "is", "filters.date.isNot": "is not", "filters.date.isBefore": "is before", "filters.date.isOnOrAfter": "is on or after", "filters.date.isAfter": "is after", "filters.date.isOnOrBefore": "is on or before", "filters.date.isBetween": "is between", "filters.date.isNotBetween": "is not between", "filters.text.contains": "contains", "filters.text.doesNotContain": "does not contain", "filters.number.is": "is", "filters.number.isNot": "is not", "filters.number.greaterThan": "greater than", "filters.number.greaterThanOrEqual": "greater than or equal", "filters.number.lessThan": "less than", "filters.number.lessThanOrEqual": "less than or equal", "filters.number.isBetween": "is between", "filters.number.isNotBetween": "is not between"}