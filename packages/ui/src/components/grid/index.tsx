"use client";

import React, { useRef, useCallback, useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { AgGridReact } from 'ag-grid-react';
import * as agGrid from "ag-grid-community";
import { defaultColDef } from "@relio/ui/grid/columns";
import { DownloadIcon, EllipsisIcon, MapIcon, TaskListIcon } from "@relio/ui/icons";
import { ToggleGroup, ToggleGroupItem } from "@relio/ui/toggle-group";
import { getLocalStorage, setLocalStorage } from "@relio/ui/utils/local-storage";
import { ButtonWithIcon, IconButton } from "@relio/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSub, DropdownMenuSubTrigger, DropdownMenuPortal, DropdownMenuSubContent } from "@relio/ui/dropdown-menu";
import MapView from "@relio/app/src/app/[root]/properties/map"
import type { Id } from "@relio/backend/convex/_generated/dataModel";
import { MapProvider } from "@relio/app/src/providers/MapProvider";
import { toast } from "sonner";
import {CsvImporter} from "@relio/ui/importer/csv-importer";
import { useImportContacts } from "@relio/app/src/hooks/imports/use-import-contacts";
import Loader from "@relio/ui/custom/loader";
import BottomBar from "@relio/ui/custom/bottom-bar";
import MergeRecordModal from "../modals/merge-records-modal";
import { useTheme } from "next-themes";
import { themeQuartz } from '@ag-grid-community/theming';
import LinkedPropertiesRenderer from "./linked-properties";
import './ag-grid-style.css';
import './ag-grid-light-theme.css';
import { useCurrentOrganization } from "@relio/app/src/hooks/organization/use-current-organization";
import { useCurrentUser } from "@relio/app/src/hooks/user/use-current-user";
import { useCreateImportLog } from "@relio/app/src/hooks/imports/use-create-import-log";
import { AllCommunityModule, ModuleRegistry, provideGlobalGridOptions, NumberEditorModule } from 'ag-grid-community';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@relio/ui/select";
import { ChevronLeftIcon, ChevronRightIcon } from "@relio/ui/icons";
import { Button } from "@relio/ui/button";
import { Input } from "@relio/ui/input";
import type { DataTableProps } from "../../types/data-table";
// Temporarily disabled data-table-filter due to type issues
// import { DataTableFilter, useDataTableFilters } from "../data-table-filter";
// import type { ColumnConfig, FiltersState } from "../data-table-filter/core/types";
import Filters from "../filters";
import { Filter, FilterType, FilterOperator } from "../filters";
import type { FilterCondition } from "../filters/data-filter";
type FiltersState = Filter[];
import { contactFields, propertyFields } from "./data";
import { UserIcon, MailIcon, PhoneIcon, TagIcon, GlobeIcon, BuildingIcon, MapPinIcon } from "lucide-react";
import { RecordType } from "@relio/types";

ModuleRegistry.registerModules([AllCommunityModule, NumberEditorModule]);

const PAGE_SIZE_OPTIONS = [25, 50, 100, 250, 500, 1000];

interface GridProps extends DataTableProps<any> {
  columnDefs: agGrid.ColDef[];
  recordType: string;
  userId: Id<"users">;
  orgId: Id<"organization">;
  list?: boolean;
  tag?: boolean;
  buyerNeeds?: boolean;
  onFilterChange?: (filters: FilterCondition[]) => void;
}

const Grid = ({
  columnDefs,
  recordType,
  userId,
  orgId,
  data,
  list = false,
  tag = false,
  buyerNeeds = false,
  totalRecords = 0,
  isLoading = false,
  getPage,
  onPageSizeChange,
  onCursorChange,
  pageSize = 100,
  cursor = null,
  currentPage = 1,
  onFilterChange,
}: GridProps) => {
  const { theme } = useTheme();
  const router = useRouter();
  const [rowData, setRowData] = useState<any[]>(data);
  const [gridApi, setGridApi] = useState<agGrid.GridApi | null>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [gridColumnApi, setGridColumnApi] = useState<agGrid.GridApi | null>(null);
  const [selectedRecords, setSelectedRecords] = useState<any[]>([]);
  const [mapValue, setMapValue] = useState(() => {
    const storedValue = getLocalStorage('relio:properties_view');
    return storedValue || 'list';
  });
  const [showBottomBar, setShowBottomBar] = useState(false);
  const [filteredData, setFilteredData] = useState<any[]>(data);
  const [selectedProperty, setSelectedProperty] = useState<any | null>(null);
  const [polygonSearchActive, setPolygonSearchActive] = useState(false);
  const drawRef = useRef<MapboxDraw | null>(null);
  const { data: organization } = useCurrentOrganization();
  const { user } = useCurrentUser();
  const { mutate: createImportLog } = useCreateImportLog();
  const { mutate: importContacts } = useImportContacts();
  const [isDataReady, setIsDataReady] = useState(false);
  const [filterModel, setFilterModel] = useState<any>(null);
  const [showAggregations, setShowAggregations] = useState(true);
  const [footerDropdowns, setFooterDropdowns] = useState<{ [key: string]: string }>({});
  const [bottomData, setBottomData] = useState<any[]>([]);
  const [internalCurrentPage, setInternalCurrentPage] = useState(currentPage);
  const [hasMoreRecords, setHasMoreRecords] = useState(false);
  const [totalPages, setTotalPages] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [pageInputValue, setPageInputValue] = useState<string>(currentPage.toString());

  const [filters, setFilters] = useState<FilterCondition[]>([]);

  // Update filteredData when filters change (implement your own filtering logic as needed)
  useEffect(() => {
    // TODO: Implement actual filtering logic based on filters
    // For now, just call onFilterChange if provided
    if (onFilterChange) onFilterChange(filters);
  }, [filters, onFilterChange]);

  const topGrid = useRef(null);
  const bottomGrid = useRef(null);

  // Update internalCurrentPage when currentPage prop changes
  useEffect(() => {
    setInternalCurrentPage(currentPage);
    setPageInputValue(currentPage.toString());
  }, [currentPage]);

  useEffect(() => {
    if (data && data.length > 0) {
      console.log('Grid received data:', data.length, 'records, total:', totalRecords);

      // Set the row data
      setRowData(data);
      setFilteredData(data);
      setIsDataReady(true);
      updateBottomData(data);

      // Check if there are more records to load
      const moreRecordsExist = data.length < totalRecords;
      setHasMoreRecords(moreRecordsExist);

      // Calculate total pages - ensure we always have at least 1 page
      const calculatedTotalPages = Math.max(1, Math.ceil(totalRecords / pageSize));
      setTotalPages(calculatedTotalPages);
    } else {
      console.log('Grid received no data or empty data');
      setRowData([]);
      setFilteredData([]);
      setIsDataReady(true);
      updateBottomData([]);
      setHasMoreRecords(false);
      setTotalPages(1);
    }
  }, [data, recordType, totalRecords, pageSize]);

  // Update page input value when internalCurrentPage changes
  useEffect(() => {
    setPageInputValue(internalCurrentPage.toString());
    console.log(`Updating page display to: ${internalCurrentPage}`);
  }, [internalCurrentPage]);

  useEffect(() => {
    if (gridApi) {
      const handleSelectionChanged = () => {
        if (gridApi.isDestroyed()) {
          return;
        }
        const records = gridApi.getSelectedRows() as any[];
        setSelectedRecords(records);
        setShowBottomBar(records.length > 0);
      };

      gridApi.addEventListener("selectionChanged", handleSelectionChanged);

      return () => {
        // Check if the grid API is still valid before removing the event listener
        if (gridApi && !gridApi.isDestroyed()) {
          try {
            gridApi.removeEventListener("selectionChanged", handleSelectionChanged);
          } catch (error) {
            console.warn('Failed to remove event listener, grid may have been destroyed:', error);
          }
        }
      };
    }
  }, [gridApi]);

  useEffect(() => {
    if (recordType !== 'properties') {
      setMapValue('list');
    } else {
      const storedValue = getLocalStorage('relio:properties_view');
      setMapValue(storedValue || 'list');
    }
  }, [recordType]);

  const onGridReady = (params: any) => {
    setGridApi(params.api);
    setGridColumnApi(params.columnApi);

    if (filterModel) {
      params.api.setFilterModel(filterModel);
    }
  };

  const updateBottomData = (currentData: any[]) => {
    const footerData = generateFooterData(currentData, columnDefs);
    setBottomData([footerData]);
  };

  const onFilterChanged = useCallback((params: any) => {
    const filteredRows = params.api.getRenderedNodes().map((node: any) => node.data);
    setFilteredData(filteredRows);
    updateBottomData(filteredRows);

    const currentFilterModel = params.api.getFilterModel();
    setLocalStorage(`relio:${recordType}_filter_model`, JSON.stringify(currentFilterModel));
  }, [recordType]);

  const onSelectionChanged = (event: agGrid.SelectionChangedEvent) => {
    const selectedRows = event.api.getSelectedRows();
    if (selectedRows.length > 0) {
      setSelectedProperty({ ...selectedRows[0], source: 'database' });
    } else {
      setSelectedProperty(null);
    }
  };

  const togglePolygonSearch = () => {
    setPolygonSearchActive(!polygonSearchActive);

    if (!polygonSearchActive) {
      drawRef.current?.changeMode('draw_polygon');
    } else {
      drawRef.current?.deleteAll();
      drawRef.current?.changeMode('simple_select');
      setFilteredData(data);
      setRowData(data);
    }
  };

  const handleImport = async (formattedData: any[]) => {
    if (recordType === 'contacts') {
      await importContacts({
          orgId: organization?._id as Id<"organization">,
          importedData: formattedData
        }, {
          onSuccess: (data) => {
            toast.success(data.message)
          },
          onError: (error) => {
            toast.error(error.message)
          },
          onFinally: () => {
            console.log("import finally");
          },
        })
    }

    await createImportLog({
      orgId: organization?._id as Id<"organization">,
      name: uploadedFile?.name ?? '',
      type: uploadedFile?.type ?? '',
      userId: user!._id,
      size: uploadedFile?.size ?? 0,
      preview: uploadedFile ? await uploadedFile.text() : '',
    })
  }

  const handleFooterDropdownSelect = (field: string, value: string) => {
    setFooterDropdowns(prev => ({ ...prev, [field]: value }));
  };

  const generateFooterData = (rowData: any[], columnDefs: agGrid.ColDef[]) => {
    const footerData: { [key: string]: any } = {};

    if (columnDefs[0]?.field) {
      footerData[columnDefs[0].field as string] = `${rowData.length} count`;
    }

    // columnDefs.forEach((colDef, index) => {
    //   if (colDef.field && index > 0) {
    //     footerData[colDef.field as string] = {
    //       cellRenderer: FooterComponent,
    //       cellRendererParams: {
    //         footerCalculations: [
    //           { type: 'count', label: 'Count' },
    //           { type: 'sum', label: 'Sum' },
    //           { type: 'average', label: 'Average' },
    //           { type: 'min', label: 'Min' },
    //           { type: 'max', label: 'Max' },
    //           { type: 'emptyCount', label: 'Empty Count' },
    //           { type: 'filledCount', label: 'Filled Count' },
    //           { type: 'percentEmpty', label: 'Percent Empty' },
    //           { type: 'percentFilled', label: 'Percent Filled' },
    //         ],
    //         column: colDef,
    //         api: gridApi,
    //       },
    //     };
    //   }
    // });

    return footerData;
  };

  const recordCellRenderer = useMemo(() => {
    return LinkedPropertiesRenderer;
  }, []);

  const handleMapValueChange = (value: string) => {
    if (recordType === 'properties') {
      setMapValue(value);
      setLocalStorage('relio:properties_view', value);
    }
  };

  const handlePageSizeChange = (newSize: string) => {
    const size = parseInt(newSize, 10);
    if (onPageSizeChange) {
      onPageSizeChange(size);
    }
    setInternalCurrentPage(1);
    if (onCursorChange) {
      onCursorChange(null);
    }
  };

  const handleLoadMore = async () => {
    if (!getPage || !orgId || isLoadingMore) return;

    try {
      setIsLoadingMore(true);

      console.log(`Loading more records, current page: ${internalCurrentPage}, cursor: ${cursor || 'null'}`);

      const result = await getPage({
        startRow: 0,
        endRow: pageSize,
        orgId,
        cursor
      });

      if (result.success && result.rows.length > 0) {
        // Update the row data with the new page
        setRowData(result.rows);

        // Update the current page number
        const nextPage = internalCurrentPage + 1;
        setInternalCurrentPage(nextPage);
        setPageInputValue(nextPage.toString());

        // Update the cursor for the next page
        if (onCursorChange && result.cursor) {
          console.log(`Updating cursor to: ${result.cursor}`);
          onCursorChange(result.cursor);
        } else if (!result.cursor) {
          console.log('No cursor returned from API, cannot load more records');
          setHasMoreRecords(false);
        }

        toast.success(`Navigated to page ${nextPage}`);
      } else {
        // No more records to load
        setHasMoreRecords(false);
        console.log('No more records to load or API returned an error');
        toast.error('No more records to load');
      }
    } catch (error) {
      console.error('Error loading more records:', error);
      toast.error('Failed to load more records');
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Function to handle going to the last page
  const handleGoToLastPage = async () => {
    if (!getPage || !orgId || isLoadingMore || internalCurrentPage === totalPages) return;

    try {
      setIsLoadingMore(true);

      // For cursor-based pagination, we need to load all pages up to the last one
      // This is a limitation of cursor-based pagination
      let currentCursor = cursor;
      let allRecords = [...rowData];
      let currentPageCount = internalCurrentPage;

      // Show loading indicator
      toast.info(`Loading pages ${internalCurrentPage + 1} to ${totalPages}...`);

      // Load pages until we reach the last page
      while (currentPageCount < totalPages) {
        const result = await getPage({
          startRow: 0,
          endRow: pageSize,
          orgId,
          cursor: currentCursor
        });

        if (result.success && result.rows.length > 0) {
          // Add the new rows to our accumulated records
          allRecords = [...allRecords, ...result.rows];

          // Update the UI with progress
          setRowData(allRecords);

          // Update the cursor for the next page
          currentCursor = result.cursor ?? null;
          currentPageCount++;

          // Update progress
          toast.info(`Loaded page ${currentPageCount} of ${totalPages}`);

          // If we've reached the last page or there's no more cursor, stop
          if (!result.cursor || currentPageCount >= totalPages) {
            break;
          }
        } else {
          // No more records to load
          break;
        }
      }

      // Update the cursor for future pagination
      if (onCursorChange) {
        onCursorChange(currentCursor);
      }

      // Update the current page
      setInternalCurrentPage(currentPageCount);

      // We're at the last page, so there are no more records
      setHasMoreRecords(false);

      toast.success(`Navigated to the last page (${currentPageCount})`);
    } catch (error) {
      console.error('Error loading last page:', error);
      toast.error('Failed to load last page');
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Function to handle jumping to a specific page
  const handleJumpToPage = async (targetPage: number) => {
    if (!getPage || !orgId || isLoadingMore || targetPage < 1 || targetPage > totalPages || targetPage === internalCurrentPage) return;

    try {
      setIsLoadingMore(true);

      if (targetPage < internalCurrentPage) {
        // Going backwards requires resetting to page 1
        if (onCursorChange) {
          onCursorChange(null);
        }
        toast.info('Returning to first page');
        return;
      }

      // Going forward from current page
      let currentCursor = cursor;
      let currentPageCount = internalCurrentPage;

      // Load pages until we reach the target
      while (currentPageCount < targetPage) {
        const result = await getPage({
          startRow: 0,
          endRow: pageSize,
          orgId,
          cursor: currentCursor
        });

        if (result.success && result.rows.length > 0) {
          // Update the cursor for the next page
          currentCursor = result.cursor ?? null;
          currentPageCount++;

          // If we've reached our target page or there's no more cursor, stop
          if (!result.cursor || currentPageCount >= targetPage) {
            break;
          }
        } else {
          // No more records to load
          break;
        }
      }

      // Update the cursor for future pagination
      if (onCursorChange) {
        onCursorChange(currentCursor);
      }

      toast.success(`Navigated to page ${currentPageCount}`);
    } catch (error) {
      console.error('Error jumping to page:', error);
      toast.error('Failed to jump to page');
    } finally {
      setIsLoadingMore(false);
    }
  };

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers
    if (/^\d*$/.test(value)) {
      setPageInputValue(value);
    }
  };

  const handlePageInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const targetPage = parseInt(pageInputValue, 10);
      if (!isNaN(targetPage) && targetPage >= 1 && targetPage <= totalPages) {
        handleJumpToPage(targetPage);
      } else {
        // Reset to current page if invalid
        setPageInputValue(internalCurrentPage.toString());
        toast.error(`Please enter a valid page number between 1 and ${totalPages}`);
      }
    }
  };

  // Add this function to log the current pagination state
  const logPaginationState = () => {
    console.log('Current pagination state:', {
      internalCurrentPage,
      totalPages,
      pageSize,
      cursor,
      hasMoreRecords,
      totalRecords,
      loadedRecords: rowData.length,
      isLoadingMore
    });

    toast.info(`Page ${internalCurrentPage} of ${totalPages}, ${rowData.length}/${totalRecords} records loaded`);
  };

  // Add this function to calculate the current page range
  const getCurrentPageRange = () => {
    const start = (internalCurrentPage - 1) * pageSize + 1;
    const end = Math.min(start + rowData.length - 1, internalCurrentPage * pageSize);
    return { start, end };
  };

  // The filter changes are now handled by the useDataTableFilters hook

  if (!isDataReady) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader />
      </div>
    );
  }

  return (
    <div style={{ height: "calc(100vh - 3rem)" }}>
        <div id="map-navbar" className="p-2 w-full border-b flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* Temporarily disabled filters due to type mismatch */}
            {/* <Filters filters={filters} setFilters={setFilters} /> */}
          </div>

          <div className="flex flex-row items-center space-x-1">
            {mapValue === 'map' && (
              <ButtonWithIcon
                icon={polygonSearchActive ? "close" : "polygon"}
                onClick={togglePolygonSearch}
              >
                {polygonSearchActive ? "Cancel Search" : "Polygon Search"}
              </ButtonWithIcon>
            )}
            {recordType === 'properties' && (
              <ToggleGroup
                size="sm"
                type="single"
                className={'border border-muted rounded-lg p-0.5'}
                onValueChange={handleMapValueChange}
                value={mapValue}
              >
                <ToggleGroupItem value="list" aria-label="list" className={'!px-2 h-5'}>
                  <TaskListIcon />
                </ToggleGroupItem>
                <ToggleGroupItem value="map" aria-label="map" className={'!px-2 h-5'}>
                  <MapIcon />
                </ToggleGroupItem>
              </ToggleGroup>
            )}
            <IconButton
                icon={<DownloadIcon size={12} />}
                tooltip={`Download data grid as CSV`}
                side="bottom"
                onClick={() => gridApi?.exportDataAsCsv()}
              />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <IconButton
                  icon={<EllipsisIcon size={12} />}
                  onClick={() => console.log('import')}
                />
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className='rounded-xl'>
                <DropdownMenuItem asChild className="p-0 m-0 rounded-md">
                <CsvImporter
                  button={
                    <ButtonWithIcon
                      className="!bg-transparent border-none hover:!bg-transparent"
                      icon="import"
                    >
                      Import CSV
                    </ButtonWithIcon>
                  }
                  fields={recordType === "properties" ? propertyFields : contactFields}
                  setUploadedFile={setUploadedFile}
                  onImport={(parsedData) => {
                    const formattedData: any["importedData"] = parsedData.map(
                      (item) => ({
                        firstName: item.firstName,
                        lastName: item.lastName,
                        fullName: item.fullName,
                        persona: item.persona,
                        status: item.status,
                        title: item.title,
                        address: item.address,
                        phone: item.phone,
                        email: item.email,
                        website: item.website,
                        linkedin: item.linkedin,
                        facebook: item.facebook,
                        twitter: item.twitter,
                        instagram: item.instagram,
                        source: item.source,
                        stage: item.stage,
                        birthday: item.birthday,
                        spouseName: item.spouseName,
                        summary: item.summary,
                        tags: item.tags,
                        buyerNeeds: item.buyerNeeds,
                      })
                    )
                    handleImport(formattedData)
                  }}
                  className="self-end"
                />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
      </div>
    <div
      style={{
        height: "calc(100% - 35px)",
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {mapValue === 'map' && (
        <div style={{ height: '70%', minHeight: '300px' }}>
          <MapProvider>
            <MapView
              gridApi={gridApi as agGrid.GridApi<any>}
              data={filteredData}
              setRowData={setRowData}
              selectedProperty={selectedProperty}
              setSelectedProperty={setSelectedProperty}
              drawRef={drawRef}
              filteredData={filteredData}
              setFilteredData={setFilteredData}
              polygonSearchActive={polygonSearchActive}
            />
          </MapProvider>
        </div>
      )}
      <div
        className="grid-container-wrapper h-full"
        style={{
          height: mapValue === 'map' ? '30%' : '100%',
          minHeight: mapValue === 'map' ? '200px' : 'auto',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <div className="grid-wrapper" style={{ flex: 1, overflow: 'hidden' }}>
          <div className="h-full w-full">
            <AgGridReact
              ref={topGrid}
              alignedGrids={[bottomGrid]}
              rowData={rowData}
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              defaultColDef={defaultColDef}
              animateRows={true}
              suppressServerSideFullWidthLoadingRow={true}
              onFilterChanged={onFilterChanged}
              onSelectionChanged={onSelectionChanged}
              rowHeight={mapValue === 'map' ? 35 : 40}
              rowBuffer={0}
              suppressColumnVirtualisation={true}
              onFilterModified={filterModel}
              suppressHorizontalScroll
              overlayNoRowsTemplate={`<span className="ag-overlay-loading-center">No ${recordType} found</span>`}
              rowSelection="multiple"
              detailRowAutoHeight={true}
              components={{
                detailCellRenderer: recordCellRenderer
              }}
              enableBrowserTooltips={true}
              enableCellTextSelection={true}
              enableGroupEdit={true}
              undoRedoCellEditing={true}
              undoRedoCellEditingLimit={5}
              domLayout="normal"
              headerHeight={40}
              theme={"legacy"}
              pagination={false}
              getRowId={(params) => {
                return params.data.id || params.data._id || `row-${Math.random().toString(36).substring(2, 9)}`;
              }}
            />
            </div>
          </div>

          {totalRecords > 0 && (
            <div className="flex items-center justify-between px-4 py-2 border-t bg-muted/20">
              <div className="flex items-center text-sm text-muted-foreground">
                <span>
                  {(() => {
                    const { start, end } = getCurrentPageRange();
                    return (
                      <>
                        Showing <span className="font-medium">{start}</span> to{" "}
                        <span className="font-medium">{end}</span> of{" "}
                        <span className="font-medium">{totalRecords}</span> records
                      </>
                    );
                  })()}
                </span>

                <div className="ml-4">
                  <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
                    <SelectTrigger className="w-[100px] h-8">
                      <SelectValue placeholder="Page size" />
                    </SelectTrigger>
                    <SelectContent>
                      {PAGE_SIZE_OPTIONS.map(size => (
                        <SelectItem key={size} value={size.toString()}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Reset to first page
                    setInternalCurrentPage(1);
                    setPageInputValue('1');
                    setHasMoreRecords(true);
                    if (onCursorChange) {
                      onCursorChange(null);
                    }
                  }}
                  disabled={internalCurrentPage === 1 || isLoading || isLoadingMore}
                  className="h-8 w-8 p-0"
                  title="First page"
                >
                  <ChevronLeftIcon className="h-4 w-4" />
                  <span className="sr-only">First page</span>
                </Button>

                <div className="flex items-center space-x-1">
                  {isLoadingMore ? (
                    <span className="inline-flex items-center text-sm">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading
                    </span>
                  ) : (
                    <>
                      <Input
                        type="text"
                        value={pageInputValue}
                        onChange={handlePageInputChange}
                        onKeyDown={handlePageInputKeyDown}
                        className="w-12 h-8 text-center border-none"
                        disabled={isLoading || isLoadingMore}
                        title="Enter page number and press Enter"
                      />
                      <span className="text-sm text-muted-foreground">of {totalPages}</span>
                    </>
                  )}
                </div>

                {hasMoreRecords && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleLoadMore}
                      disabled={isLoading || isLoadingMore}
                      className="h-8 w-8 p-0"
                      title="Next page"
                    >
                      <ChevronRightIcon className="h-4 w-4" />
                      <span className="sr-only">Next page</span>
                    </Button>
                )}
              </div>
            </div>
          )}
      </div>
    </div>

      {showBottomBar && <BottomBar selectedRecords={selectedRecords} recordType={recordType} />}
      <MergeRecordModal
        orgId={organization?._id as Id<"organization">}
        selectedRecords={selectedRecords}
        recordType={recordType as RecordType}
      />
    </div>
  );
};

export default Grid;
