import type { ColumnConfig } from "../data-table-filter/core/types";
import { UserIcon, MailIcon, PhoneIcon, MapPinIcon, CalendarIcon, TagIcon, BuildingIcon, GlobeIcon, LinkedinIcon, FacebookIcon, TwitterIcon, InstagramIcon } from "lucide-react";

// Contact column configurations for data-table-filter
export function createContactColumnConfigs<TData>(): ColumnConfig<TData>[] {
  return [
    {
      id: "fullName",
      accessor: (data: any) => data.fullName || "",
      displayName: "Full Name",
      icon: UserIcon,
      type: "text",
    },
    {
      id: "firstName",
      accessor: (data: any) => data.firstName || "",
      displayName: "First Name", 
      icon: UserIcon,
      type: "text",
    },
    {
      id: "lastName",
      accessor: (data: any) => data.lastName || "",
      displayName: "Last Name",
      icon: UserIcon,
      type: "text",
    },
    {
      id: "email",
      accessor: (data: any) => {
        if (!data.email || !Array.isArray(data.email)) return "";
        const primaryEmail = data.email.find((e: any) => e.isPrimary);
        return primaryEmail?.address || data.email[0]?.address || "";
      },
      displayName: "Email",
      icon: MailIcon,
      type: "text",
    },
    {
      id: "phone",
      accessor: (data: any) => {
        if (!data.phone || !Array.isArray(data.phone)) return "";
        const primaryPhone = data.phone.find((p: any) => p.isPrimary);
        return primaryPhone?.number || data.phone[0]?.number || "";
      },
      displayName: "Phone",
      icon: PhoneIcon,
      type: "text",
    },
    {
      id: "title",
      accessor: (data: any) => data.title || "",
      displayName: "Title",
      icon: UserIcon,
      type: "text",
    },
    {
      id: "persona",
      accessor: (data: any) => data.persona || "",
      displayName: "Persona",
      icon: UserIcon,
      type: "option",
      options: [
        { label: "Buyer", value: "buyer" },
        { label: "Seller", value: "seller" },
        { label: "Investor", value: "investor" },
        { label: "Agent", value: "agent" },
        { label: "Lender", value: "lender" },
        { label: "Other", value: "other" },
      ],
    },
    {
      id: "status",
      accessor: (data: any) => data.status || "",
      displayName: "Status",
      icon: TagIcon,
      type: "option",
      options: [
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
        { label: "Lead", value: "lead" },
        { label: "Client", value: "client" },
        { label: "Past Client", value: "past_client" },
      ],
    },
    {
      id: "stage",
      accessor: (data: any) => data.stage || "",
      displayName: "Stage",
      icon: TagIcon,
      type: "option",
      options: [
        { label: "New", value: "new" },
        { label: "Contacted", value: "contacted" },
        { label: "Qualified", value: "qualified" },
        { label: "Proposal", value: "proposal" },
        { label: "Negotiation", value: "negotiation" },
        { label: "Closed Won", value: "closed_won" },
        { label: "Closed Lost", value: "closed_lost" },
      ],
    },
    {
      id: "source",
      accessor: (data: any) => data.source || "",
      displayName: "Source",
      icon: GlobeIcon,
      type: "option",
      options: [
        { label: "Website", value: "website" },
        { label: "Referral", value: "referral" },
        { label: "Social Media", value: "social_media" },
        { label: "Cold Call", value: "cold_call" },
        { label: "Email Campaign", value: "email_campaign" },
        { label: "Event", value: "event" },
        { label: "Other", value: "other" },
      ],
    },
    {
      id: "website",
      accessor: (data: any) => data.website || "",
      displayName: "Website",
      icon: GlobeIcon,
      type: "text",
    },
    {
      id: "linkedin",
      accessor: (data: any) => data.linkedin || "",
      displayName: "LinkedIn",
      icon: LinkedinIcon,
      type: "text",
    },
    {
      id: "facebook",
      accessor: (data: any) => data.facebook || "",
      displayName: "Facebook",
      icon: FacebookIcon,
      type: "text",
    },
    {
      id: "twitter",
      accessor: (data: any) => data.twitter || "",
      displayName: "Twitter",
      icon: TwitterIcon,
      type: "text",
    },
    {
      id: "instagram",
      accessor: (data: any) => data.instagram || "",
      displayName: "Instagram",
      icon: InstagramIcon,
      type: "text",
    },
    {
      id: "birthday",
      accessor: (data: any) => data.birthday ? new Date(data.birthday) : null,
      displayName: "Birthday",
      icon: CalendarIcon,
      type: "date",
    },
    {
      id: "spouseName",
      accessor: (data: any) => data.spouseName || "",
      displayName: "Spouse Name",
      icon: UserIcon,
      type: "text",
    },
    {
      id: "summary",
      accessor: (data: any) => data.summary || "",
      displayName: "Summary",
      icon: UserIcon,
      type: "text",
    },
  ];
}

// Property column configurations for data-table-filter
export function createPropertyColumnConfigs<TData>(): ColumnConfig<TData>[] {
  return [
    {
      id: "name",
      accessor: (data: any) => data.name || "",
      displayName: "Property Name",
      icon: BuildingIcon,
      type: "text",
    },
    {
      id: "address.street",
      accessor: (data: any) => data.address?.street || "",
      displayName: "Street Address",
      icon: MapPinIcon,
      type: "text",
    },
    {
      id: "address.city",
      accessor: (data: any) => data.address?.city || "",
      displayName: "City",
      icon: MapPinIcon,
      type: "text",
    },
    {
      id: "address.state",
      accessor: (data: any) => data.address?.state || "",
      displayName: "State",
      icon: MapPinIcon,
      type: "text",
    },
    {
      id: "address.zip",
      accessor: (data: any) => data.address?.zip || "",
      displayName: "ZIP Code",
      icon: MapPinIcon,
      type: "text",
    },
    {
      id: "address.county",
      accessor: (data: any) => data.address?.county || "",
      displayName: "County",
      icon: MapPinIcon,
      type: "text",
    },
    {
      id: "propertyType",
      accessor: (data: any) => data.propertyType || "",
      displayName: "Property Type",
      icon: BuildingIcon,
      type: "option",
      options: [
        { label: "Single Family", value: "single_family" },
        { label: "Multi Family", value: "multi_family" },
        { label: "Condo", value: "condo" },
        { label: "Townhouse", value: "townhouse" },
        { label: "Commercial", value: "commercial" },
        { label: "Land", value: "land" },
        { label: "Other", value: "other" },
      ],
    },
    {
      id: "propertySubType",
      accessor: (data: any) => data.propertySubType || "",
      displayName: "Property Sub Type",
      icon: BuildingIcon,
      type: "text",
    },
    {
      id: "market",
      accessor: (data: any) => data.market || "",
      displayName: "Market",
      icon: MapPinIcon,
      type: "text",
    },
    {
      id: "subMarket",
      accessor: (data: any) => data.subMarket || "",
      displayName: "Sub Market",
      icon: MapPinIcon,
      type: "text",
    },
    {
      id: "listingId",
      accessor: (data: any) => data.listingId || "",
      displayName: "Listing ID",
      icon: TagIcon,
      type: "text",
    },
  ];
}
