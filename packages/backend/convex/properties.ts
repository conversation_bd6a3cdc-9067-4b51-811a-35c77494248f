import { action, ActionCtx, internalMutation, internalQuery, mutation, query, QueryCtx } from "./_generated/server";
import { ConvexError, v } from "convex/values";
import { Id } from "./_generated/dataModel"
import { hasAccessToOrg, user } from "./helpers/standardHelpers";
import { internal } from "./_generated/api";
import { recordTypes } from "./schema";
import { updateRecordWithListId } from "./lists";
import { getRandomColor } from "@relio/ui/utils";
import { paginationOptsValidator } from "convex/server";
import { updateRecord, recordViewed } from "./records";
import { aggregateProperties } from "./custom";
import {
  calculateSafeBatchSize,
  handleFavoritesQuery,
  handleTextSearchQuery,
  tryFieldSpecificSearch,
  buildBaseQuery,
  applyFilters,
  applySorting,
  applyPagination
} from "./utils";
import { applyFiltersToQuery } from "@relio/ui/utils/filter-to-convex";

const propertyArgs = {
  name: v.string(),
  image: v.optional(v.string()),
  orgId: v.id("organization"),
  propertyType: v.optional(v.string()),
  propertySubType: v.optional(v.string()),
  market: v.optional(v.string()),
  subMarket: v.optional(v.string()),
  listingId: v.optional(v.string()),
  address: v.object({
    street: v.string(),
    street2: v.optional(v.string()),
    city: v.string(),
    state: v.string(),
    zip: v.optional(v.float64()),
    county: v.optional(v.string()),
    country: v.optional(v.string()),
  }),
  location: v.object({
    type: v.literal("Point"),
    coordinates: v.array(v.float64()),
  }),
  website: v.optional(v.string()),
  updatedTime: v.optional(v.string()),
  lastViewedTime: v.optional(v.float64()),
  isDeleted: v.optional(v.boolean()),
  deletedTime: v.optional(v.string()),
  deletedBy: v.optional(v.id("users")),
  tags: v.optional(v.array(v.id("tags"))),
  lists: v.optional(v.array(v.id("lists"))),
  files: v.optional(v.array(v.string())),
  tasks: v.optional(v.array(v.id("tasks"))),
  mongoId: v.optional(v.string()),
  recordType: v.optional(v.literal("properties")),
  status: v.optional(v.string()),
  units: v.optional(v.float64()),
  yearBuilt: v.optional(v.float64()),
  squareFootage: v.optional(v.float64()),
  price: v.optional(v.float64()),
  parcelNumber: v.optional(v.string()),
  saleDate: v.optional(v.string()),
  salePrice: v.optional(v.float64()),
  landValue: v.optional(v.float64()),
  buildingValue: v.optional(v.float64()),
  primaryUse: v.optional(v.string()),
  construction: v.optional(v.string()),
  lotSize: v.optional(v.float64()),
  zoning: v.optional(v.string()),
  meterType: v.optional(v.string()),
  class: v.optional(v.string()),
  structures: v.optional(v.float64()),
  parking: v.optional(v.string()),
};

async function createProperty(ctx: any, args: any, enrichment = false) {
  const currentUser = await user(ctx);
  const currentOrg = await ctx.db.get(args.orgId) as any;
  const hasAccess = await hasAccessToOrg(ctx, args.orgId);

  if (!currentUser) throw new ConvexError("Not authenticated");
  if (!hasAccess) return [];

  const propertyId = await ctx.db.insert("properties", {
    ...args,
    isDeleted: false,
    createdBy: currentUser._id as Id<"users">,
    recordType: "properties",
  });

  const propertyUrl = `${process.env.SITE_URL}/${currentOrg.slug}/properties/${propertyId}`;

  await ctx.db.insert("activities", {
    userId: currentUser._id as Id<"users">,
    orgId: args.orgId,
    recordId: propertyId,
    system: true,
    type: "create",
    message: `${args.name} was created by ${currentUser.name}`,
  });

  await ctx.scheduler.runAfter(
    0,
    enrichment
      ? internal.ingest.extract.extractPropertyInfoForEnrichment
      : internal.ingest.extract.extractPropertyInfo,
    {
      propertyUrl,
      propertyId,
      userId: currentUser._id,
      name: args.name,
      orgId: args.orgId,
      street: args.address.street,
      street2: args.address.street2,
      city: args.address.city,
      state: args.address.state,
      zip: args.address.zip,
    }
  );

  return propertyId;
}

export const createPropertyWithEnrichment = mutation({
  args: propertyArgs,
  handler: (ctx, args) => createProperty(ctx, args, true),
});

export const createPropertyWithoutEnrichment = mutation({
  args: propertyArgs,
  handler: async (ctx, args) => {
    const currentUser = await user(ctx);
    const currentOrg = await ctx.db.get(args.orgId) as any;
    const hasAccess = await hasAccessToOrg(ctx, args.orgId);

    // if (!currentUser) throw new ConvexError("Not authenticated");
    // if (!hasAccess) return [];

    const propertyId = await ctx.db.insert("properties", {
      ...args,
      isDeleted: false,
      createdBy: currentUser?._id as Id<"users">,
      recordType: "properties",
      mongoId: args.mongoId,
      status: args.status,
      units: args.units,
      yearBuilt: args.yearBuilt,
      squareFootage: args.squareFootage,
      price: args.price,
      parcelNumber: args.parcelNumber,
      saleDate: args.saleDate,
      salePrice: args.salePrice,
      landValue: args.landValue,
      buildingValue: args.buildingValue,
      primaryUse: args.primaryUse,
      construction: args.construction,
      lotSize: args.lotSize,
      zoning: args.zoning,
      meterType: args.meterType,
      class: args.class,
      structures: args.structures,
      parking: args.parking,
      address: {
        street: args.address.street,
        street2: args.address.street2,
        city: args.address.city,
        state: args.address.state,
        zip: args.address.zip,
        county: args.address.county,
        country: args.address.country,
      },
    });

    const propertyUrl = `${process.env.SITE_URL}/${currentOrg.slug}/properties/${propertyId}`;

    await ctx.db.insert("activities", {
      userId: currentUser?._id as Id<"users">,
      orgId: args.orgId,
      recordId: propertyId,
      system: true,
      type: "create",
      message: `${args.name} was created by ${currentUser?.name}`,
    });

    await ctx.scheduler.runAfter(
      0,
      internal.ingest.extract.extractPropertyInfo,
      {
        propertyUrl,
        propertyId,
        userId: currentUser?._id as Id<"users">,
        name: args.name,
        orgId: args.orgId,
        street: args.address.street,
        street2: args.address.street2,
        city: args.address.city,
        state: args.address.state,
        zip: args.address.zip,
      }
    );

    return propertyId;
  },
});

export const propertyViewed = mutation({
  args: {
    id: v.id("properties"),
    orgId: v.id("organization"),
  },
  handler: async (ctx, args) => {
    await recordViewed(ctx, {
      ...args,
      recordType: "properties",
    });

    return {
      message: "Property viewed successfully",
      status: "success",
    }
  }
});

export const updateProperty = mutation({
  args: {
    id: v.id("properties"),
    orgId: v.id("organization"),
    address: v.optional(v.object({
      street: v.optional(v.string()),
      street2: v.optional(v.string()),
      city: v.optional(v.string()),
      state: v.optional(v.string()),
      zip: v.optional(v.float64()),
      county: v.optional(v.string()),
      country: v.optional(v.string()),
    })),
    propertyType: v.optional(v.string()),
    status: v.optional(v.string()),
    primaryUse: v.optional(v.string()),
    parcelNumber: v.optional(v.string()),
    market: v.optional(v.string()),
    subMarket: v.optional(v.string()),
    yearBuilt: v.optional(v.float64()),
    squareFootage: v.optional(v.float64()),
    construction: v.optional(v.string()),
    lotSize: v.optional(v.float64()),
    lotType: v.optional(v.string()),
    zoning: v.optional(v.string()),
    meterType: v.optional(v.string()),
    class: v.optional(v.string()),
    structures: v.optional(v.float64()),
    floors: v.optional(v.float64()),
    parking: v.optional(v.string()),
    absenteeOwner: v.optional(v.boolean()),
    auction: v.optional(v.boolean()),
    cashBuyer: v.optional(v.boolean()),
    corporateOwned: v.optional(v.boolean()),
    createdBy: v.optional(v.string()),
    deedInLieu: v.optional(v.boolean()),
    demographics: v.optional(v.string()),
    equityPercent: v.optional(v.float64()),
    estimatedEquity: v.optional(v.float64()),
    estimatedValue: v.optional(v.float64()),
    floodZone: v.optional(v.boolean()),
    floodZoneDescription: v.optional(v.string()),
    floodZoneType: v.optional(v.string()),
    foreclosureInfo: v.optional(v.array(v.string())),
    freeClear: v.optional(v.boolean()),
    highEquity: v.optional(v.boolean()),
    inStateAbsenteeOwner: v.optional(v.boolean()),
    inherited: v.optional(v.boolean()),
    investorBuyer: v.optional(v.boolean()),
    isDeleted: v.optional(v.boolean()),
    landValue: v.optional(v.float64()),
    lastSaleDate: v.optional(v.string()),
    lastUpdateDate: v.optional(v.string()),
    lastViewedBy: v.optional(v.string()),
    lastViewedTime: v.optional(v.float64()),
    legalDescription: v.optional(v.string()),
    lien: v.optional(v.boolean()),
    livingSquareFeet: v.optional(v.float64()),
    location: v.optional(v.object({
      coordinates: v.array(v.float64()),
      type: v.string(),
    })),
    lotAcres: v.optional(v.float64()),
    lotNumber: v.optional(v.string()),
    lotSquareFeet: v.optional(v.float64()),
    mlsActive: v.optional(v.boolean()),
    mlsCancelled: v.optional(v.boolean()),
    mlsDaysOnMarket: v.optional(v.float64()),
    mlsFailed: v.optional(v.boolean()),
    mlsHasPhotos: v.optional(v.boolean()),
    mlsHistory: v.optional(v.array(v.string())),
    mlsListingPrice: v.optional(v.float64()),
    mlsListingPricePerSquareFoot: v.optional(v.float64()),
    mlsPending: v.optional(v.boolean()),
    mlsSold: v.optional(v.boolean()),
    mlsStatus: v.optional(v.string()),
    mlsType: v.optional(v.string()),
    mobileHome: v.optional(v.boolean()),
    mortgages: v.optional(v.array(v.any())),
    name: v.optional(v.string()),
    neighborhood: v.optional(v.object({
      location: v.object({
        coordinates: v.array(v.float64()),
        type: v.string(),
      }),
      name: v.string(),
      type: v.string(),
    })),
    outOfStateAbsenteeOwner: v.optional(v.boolean()),
    ownerOccupied: v.optional(v.boolean()),
    parkingSpaces: v.optional(v.float64()),
    preForeclosure: v.optional(v.boolean()),
    pricePerSquareFoot: v.optional(v.float64()),
    propertyUse: v.optional(v.string()),
    quitClaim: v.optional(v.boolean()),
    reaId: v.optional(v.string()),
    recordType: v.optional(v.string()),
    roomsCount: v.optional(v.float64()),
    saleHistory: v.optional(v.array(v.string())),
    sheriffsDeed: v.optional(v.boolean()),
    source: v.optional(v.string()),
    spousalDeath: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
    taxInfo: v.optional(v.object({
      assessedImprovementValue: v.optional(v.float64()),
      assessedLandValue: v.optional(v.float64()),
      assessedValue: v.optional(v.float64()),
      assessmentYear: v.optional(v.float64()),
      marketImprovementValue: v.optional(v.float64()),
      marketLandValue: v.optional(v.float64()),
      marketValue: v.optional(v.float64()),
      propertyId: v.optional(v.float64()),
      taxAmount: v.optional(v.float64()),
      year: v.optional(v.float64()),
    })),
    taxLien: v.optional(v.boolean()),
    trusteeSale: v.optional(v.boolean()),
    updatedBy: v.optional(v.string()),
    updatedTime: v.optional(v.string()),
    vacant: v.optional(v.boolean()),
    warrantyDeed: v.optional(v.boolean()),
  },
  async handler(ctx, args) {
    const { id, orgId, ...updateFields } = args;
    const updatedProperty = await updateRecord(ctx, {
      id,
      orgId,
      updateFields,
      recordType: "properties",
    });

    return {
      message: "Property updated successfully",
      status: "success",
      data: updatedProperty,
    }
  }
});

export const deleteProperty = mutation({
  args: { id: v.id("properties") },
  handler: async (ctx, args) => {
    const { id } = args;

    await ctx.db.delete(id);
  },
});

export const getProperty = query({
  args: {
    id: v.id("properties"),
    orgId: v.id("organization"),
    isDeleted: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const hasAccess = await hasAccessToOrg(ctx, args.orgId);

    if (!hasAccess) {
      return [];
    }

    const property = await ctx.db.get(args.id) as any;

    if (!property) {
      throw new ConvexError("Property not found");
    }

    const activities = await ctx.db
      .query("activities")
      .withIndex("by_recordId", (q) => q.eq("recordId", args.id))
      .collect();

    const createdBy = property.createdBy ? await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("_id"), property.createdBy as Id<"users">))
      .first() : null;

    const lastViewedBy = property.lastViewedBy ? await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("_id"), property.lastViewedBy as Id<"users">))
      .first() : null;

    const updatedBy = property.updatedBy ? await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("_id"), property.updatedBy as Id<"users">))
      .first() : null;

    return {
      ...property,
      activities,
      createdBy,
      lastViewedBy,
      updatedBy,
    };;
  },
});

export const getProperties = query({
  args: {
    orgId: v.optional(v.id("organization")),
    query: v.optional(v.string()),
    favorites: v.optional(v.boolean()),
    isDeleted: v.optional(v.boolean()),
    filters: v.optional(v.array(v.object({
      field: v.string(),
      operator: v.string(),
      value: v.union(v.string(), v.number(), v.boolean())
    }))),
    paginationOpts: paginationOptsValidator,
  },
  async handler(ctx, args) {
    const currentUser = await user(ctx);
    if (!currentUser) {
      throw new ConvexError("Unauthorized");
    }

    console.log("Testing args", args);

    const hasAccess = await hasAccessToOrg(
      ctx,
      currentUser.activeOrgId as Id<"organization">
    );

    if (!hasAccess) {
      return { page: [], isDone: true, totalCount: 0 };
    }

    let propertiesQuery = ctx.db
      .query("properties")
      .withIndex("by_orgId", (q) =>
        q.eq("orgId", currentUser.activeOrgId as Id<"organization">)
      )
      .filter((q) => q.eq(q.field("isDeleted"), args.isDeleted || false))
      .order("desc");

    // Apply text search if query is provided
    if (args.query) {
      const queryValue = args.query.trim();
      if (isNaN(Number(queryValue))) {
        propertiesQuery = propertiesQuery.filter((q) =>
          q.or(
            q.eq(q.field("name"), queryValue),
            q.eq(q.field("address.street"), queryValue),
            q.eq(q.field("address.city"), queryValue),
            q.eq(q.field("address.state"), queryValue),
          )
        );
      } else {
        console.log("This should be a number", Number(queryValue));
        propertiesQuery = propertiesQuery.filter((q) =>
          q.eq(q.field("address.zip"), Number(queryValue))
        );
      }
    }

    // Apply custom filters
    if (args.filters && args.filters.length > 0) {
      console.log('Applying filters to properties query:', JSON.stringify(args.filters, null, 2));
      propertiesQuery = applyFiltersToQuery(propertiesQuery, args.filters);
    }

    // Get the filtered count if filters are applied
    let totalCount;
    if (args.filters && args.filters.length > 0) {
      let countQuery = ctx.db
        .query("properties")
        .withIndex("by_orgId", (q) =>
          q.eq("orgId", currentUser.activeOrgId as Id<"organization">)
        )
        .filter((q) => q.eq(q.field("isDeleted"), args.isDeleted || false));

      if (args.query) {
        const queryValue = args.query.trim();
        if (isNaN(Number(queryValue))) {
          countQuery = countQuery.filter((q) =>
            q.or(
              q.eq(q.field("name"), queryValue),
              q.eq(q.field("address.street"), queryValue),
              q.eq(q.field("address.city"), queryValue),
              q.eq(q.field("address.state"), queryValue),
            )
          );
        } else {
          countQuery = countQuery.filter((q) =>
            q.eq(q.field("address.zip"), Number(queryValue))
          );
        }
      }

      if (args.filters && args.filters.length > 0) {
        countQuery = applyFiltersToQuery(countQuery, args.filters);
      }

      const filteredResults = await countQuery.collect();
      totalCount = filteredResults.length;
      console.log("Filtered count:", totalCount);
    } else {
      // If no filters, get the total count
      totalCount = await getTotalPropertiesCount(ctx, { orgId: currentUser.activeOrgId });
      console.log("Total count:", totalCount);
    }

    const paginationResult = await propertiesQuery.paginate(args.paginationOpts);

    if (args.favorites) {
      const favorites = await ctx.db
        .query("favorites")
        .withIndex("by_userId_orgId_recordId", (q) =>
          q
            .eq("userId", hasAccess.user._id)
            .eq("orgId", currentUser.activeOrgId as Id<"organization">)
        )
        .collect();

      paginationResult.page = paginationResult.page.filter((property: any) =>
        favorites.some((favorite) => favorite.recordId === property._id)
      );
    }

    return {
      page: paginationResult.page,
      isDone: paginationResult.isDone,
      cursor: paginationResult.continueCursor,
      totalCount
    };
  },
});

export const checkDuplicateAddress = query({
  args: {
    orgId: v.id("organization"),
    name: v.string(),
  },
  async handler(ctx, args) {
    const hasAccess = await hasAccessToOrg(ctx, args.orgId);

    if (!hasAccess) {
      return null;
    }

    const existingProperties = await ctx.db
      .query("properties")
      .withIndex("by_orgId", (q) => q.eq("orgId", args.orgId))
      .collect();

    const duplicateProperty = existingProperties.find(property =>
      property.name.toLowerCase() === args.name.toLowerCase()
    );

    return duplicateProperty || null;
  },
});

export const getBuyerNeedsPropertiesById = query({
  args: {
    contactId: v.id("contacts"),
    orgId: v.id("organization"),
  },
  async handler(ctx, args) {
    const contact = await ctx.db.get(args.contactId);

    if (!contact || contact.orgId !== args.orgId) {
      throw new Error("Contact not found or unauthorized access");
    }

    const buyerNeeds = contact.buyerNeeds;

    if (!buyerNeeds) {
      return [];
    }

    let propertiesQuery = ctx.db
      .query("properties")
      .withIndex("by_orgId", (q) => q.eq("orgId", args.orgId));

    // Filter by property type
    if (buyerNeeds.propertyType) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.eq(q.field("propertyType"), buyerNeeds.propertyType)
      );
    }

    // Filter by number of units
    if (buyerNeeds.minUnits !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.gte(q.field("units"), buyerNeeds.minUnits!)
      );
    }
    if (buyerNeeds.maxUnits !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.lte(q.field("units"), buyerNeeds.maxUnits!)
      );
    }

    // Filter by price range
    if (buyerNeeds.minPrice !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.gte(q.field("price"), buyerNeeds.minPrice!)
      );
    }
    if (buyerNeeds.maxPrice !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.lte(q.field("price"), buyerNeeds.maxPrice!)
      );
    }

    // Filter by equity range
    if (buyerNeeds.minEquity !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.gte(q.field("equity"), buyerNeeds.minEquity!)
      );
    }
    if (buyerNeeds.maxEquity !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.lte(q.field("equity"), buyerNeeds.maxEquity!)
      );
    }

    // Filter by exchange
    if (buyerNeeds.exchange !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.eq(q.field("exchange"), buyerNeeds.exchange)
      );
    }

    // Filter by cap rate
    if (buyerNeeds.cap !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.gte(q.field("cap"), buyerNeeds.cap!)
      );
    }

    // Filter by year built range
    if (buyerNeeds.yearBuiltFrom !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.gte(q.field("yearBuilt"), buyerNeeds.yearBuiltFrom!)
      );
    }
    if (buyerNeeds.yearBuiltTo !== undefined) {
      propertiesQuery = propertiesQuery.filter((q) =>
        q.lte(q.field("yearBuilt"), buyerNeeds.yearBuiltTo!)
      );
    }

    return await propertiesQuery.collect();
  },
});

export const getOwnerContactIds = internalQuery({
  args: {
    propertyIds: v.array(v.id("properties")),
    orgId: v.id("organization"),
  },
  async handler(ctx, args) {
    const ownerIds: (Id<"contacts"> | Id<"companies">)[] = [];

    for (const propertyId of args.propertyIds) {
      const linkedContacts = await ctx.db
        .query("contactLinkedProperties")
        .filter((q) =>
          q.and(
            q.eq(q.field("orgId"), args.orgId),
            q.eq(q.field("propertyId"), propertyId),
            q.eq(q.field("relation"), "Owner")
          )
        )
        .collect();

      const linkedCompanies = await ctx.db
        .query("companiesLinkedProperties")
        .filter((q) =>
          q.and(
            q.eq(q.field("orgId"), args.orgId),
            q.eq(q.field("propertyId"), propertyId),
            q.eq(q.field("relation"), "Owner")
          )
        )
        .collect();

      console.log("linkedContacts", linkedContacts);
      console.log("linkedCompanies", linkedCompanies);

      // TODO: Get the initial company team member

      ownerIds.push(...linkedContacts.map((link) => link.contactId));
      ownerIds.push(...linkedCompanies.map((link) => link.companyId));
    }

    return ownerIds;
  },
});

export const createOwnerList = internalMutation({
  args: {
    name: v.string(),
    orgId: v.id("organization"),
    recordType: recordTypes,
    recordIds: v.array(v.id("contacts")),
  },
  async handler(ctx, args) {
    const currentUser = await user(ctx);

    if (!currentUser) {
      throw new ConvexError("Not authenticated");
    }

    const hasAccess = await hasAccessToOrg(ctx, args.orgId);

    if (!hasAccess) {
      throw new ConvexError("No access to organization");
    }

    const sharedUser = {
      userId: currentUser._id,
      role: "list:admin" as "list:admin",
    }

    const listId = await ctx.db.insert("lists", {
      name: args.name,
      orgId: args.orgId,
      userIds: [sharedUser],
      recordType: args.recordType,
      createdBy: currentUser._id,
    });

    if (args.recordIds) {
      for (const recordId of args.recordIds) {
        await updateRecordWithListId({
          ctx,
          recordId: recordId as Id<"contacts">,
          listId,
        });
      }
    }

    return listId;
  }
});

export const getPropertyOwners = action({
  args: {
    orgId: v.id("organization"),
    propertyIds: v.array(v.id("properties")),
  },
  async handler(ctx: ActionCtx, args) {
    const owners = await ctx.runQuery(internal.properties.getOwnerContactIds, {
      propertyIds: args.propertyIds,
      orgId: args.orgId,
    });

    const propertyNames = await Promise.all(
      args.propertyIds.map(async (propertyId: Id<"properties">) => {
        const property = await ctx.runQuery(internal.helpers.standardHelpers.getPropertyById, {
          propertyId,
        });
        return property?.name;
      })
    );

    const listName = await ctx.runAction(internal.ai.openai.generateText, {
      prompt: `Generate a list name for the following properties:
        ${propertyNames.join(", ")}.
        The list name should be a summary of the properties,
        maybe location, number of properties,
        number of units in the properties, etc.
        Do not add ** or - or "" or any other formatting.
      `,
      maxTokens: 10,
    });

    const listId: any = await ctx.runMutation(internal.properties.createOwnerList, {
      name: listName as string,
      orgId: args.orgId,
      recordType: "contacts",
      recordIds: owners as Id<"contacts">[],
    });

    return listId;
  },
})

export const createPropertyFromREAPI = mutation({
  args: {
    orgId: v.id("organization"),
    MFH2to4: v.optional(v.boolean()),
    MFH5plus: v.optional(v.boolean()),
    absenteeOwner: v.optional(v.boolean()),
    adjustableRate: v.optional(v.boolean()),
    assumable: v.optional(v.boolean()),
    auction: v.optional(v.boolean()),
    auctionInfo: v.optional(v.object({})),
    bankOwned: v.optional(v.boolean()),
    cashBuyer: v.optional(v.boolean()),
    cashSale: v.optional(v.boolean()),
    corporateOwned: v.optional(v.boolean()),
    currentMortgages: v.optional(v.union(
      v.array(v.any()),
      v.any()
    )),
    death: v.optional(v.boolean()),
    deathTransfer: v.optional(v.boolean()),
    deedInLieu: v.optional(v.boolean()),
    demographics: v.optional(v.object({
      fmrEfficiency: v.optional(v.float64()),
      fmrFourBedroom: v.optional(v.float64()),
      fmrOneBedroom: v.optional(v.float64()),
      fmrThreeBedroom: v.optional(v.float64()),
      fmrTwoBedroom: v.optional(v.float64()),
      fmrYear: v.optional(v.float64()),
      hudAreaCode: v.optional(v.string()),
      hudAreaName: v.optional(v.string()),
      medianIncome: v.optional(v.float64()),
      suggestedRent: v.optional(v.float64()),
    })),
    equity: v.optional(v.any()),
    equityPercent: v.optional(v.number()),
    estimatedEquity: v.optional(v.number()),
    estimatedMortgageBalance: v.optional(v.float64()),
    estimatedMortgagePayment: v.optional(v.float64()),
    estimatedValue: v.optional(v.float64()),
    floodZone: v.optional(v.boolean()),
    floodZoneDescription: v.optional(v.string()),
    floodZoneType: v.optional(v.string()),
    foreclosureInfo: v.optional(v.union(v.array(v.any()), v.any())),
    freeClear: v.optional(v.boolean()),
    highEquity: v.optional(v.boolean()),
    id: v.optional(v.number()),
    inStateAbsenteeOwner: v.optional(v.boolean()),
    inherited: v.optional(v.boolean()),
    investorBuyer: v.optional(v.boolean()),
    judgment: v.optional(v.boolean()),
    lastSale: v.optional(v.object({
      armsLength: v.optional(v.boolean()),
      book: v.optional(v.any()),
      buyerNames: v.optional(v.string()),
      documentNumber: v.optional(v.any()),
      documentType: v.optional(v.string()),
      documentTypeCode: v.optional(v.string()),
      downPayment: v.optional(v.number()),
      ltv: v.optional(v.number()),
      ownerIndividual: v.optional(v.boolean()),
      page: v.optional(v.any()),
      priorOwnerIndividual: v.optional(v.boolean()),
      priorOwnerMonthsOwned: v.optional(v.number()),
      purchaseMethod: v.optional(v.string()),
      recordingDate: v.optional(v.string()),
      saleAmount: v.optional(v.number()),
      saleDate: v.optional(v.string()),
      sellerNames: v.optional(v.string()),
      seqNo: v.optional(v.number()),
      transactionType: v.optional(v.string()),
    })),
    lastSaleDate: v.optional(v.string()),
    lastSalePrice: v.optional(v.float64()),
    lastUpdateDate: v.optional(v.string()),
    lien: v.optional(v.boolean()),
    linkedProperties: v.optional(v.object({
      ids: v.optional(v.union(v.array(v.number()), v.float64())),
      purchasedLast12mos: v.optional(v.number()),
      purchasedLast6mos: v.optional(v.number()),
      totalEquity: v.optional(v.float64()),
      totalMortgageBalance: v.optional(v.float64()),
      totalOwned: v.optional(v.float64()),
      totalValue: v.optional(v.float64()),
    })),
    loanTypeCodeFirst: v.optional(v.any()),
    loanTypeCodeSecond: v.optional(v.any()),
    loanTypeCodeThird: v.optional(v.any()),
    lotInfo: v.optional(v.object({
      apn: v.optional(v.string()),
      apnUnformatted: v.optional(v.float64()),
      censusBlock: v.optional(v.float64()),
      censusBlockGroup: v.optional(v.float64()),
      censusTract: v.optional(v.float64()),
      landUse: v.optional(v.string()),
      legalDescription: v.optional(v.string()),
      legalSection: v.optional(v.any()),
      lotAcres: v.optional(v.float64()),
      lotNumber: v.optional(v.union(v.string(), v.number(), v.float64())),
      lotSquareFeet: v.optional(v.float64()),
      propertyClass: v.optional(v.any()),
      propertyUse: v.optional(v.string()),
      subdivision: v.optional(v.union(v.string(), v.number())),
      zoning: v.optional(v.string()),
    })),
    maturityDateFirst: v.optional(v.any()),
    mlsActive: v.optional(v.boolean()),
    mlsCancelled: v.optional(v.boolean()),
    mlsDaysOnMarket: v.optional(v.any()),
    mlsFailed: v.optional(v.boolean()),
    mlsFailedDate: v.optional(v.any()),
    mlsHasPhotos: v.optional(v.boolean()),
    mlsHistory: v.optional(v.union(
      v.object({
        agentEmail: v.optional(v.string()),
        agentName: v.optional(v.string()),
        agentOffice: v.optional(v.string()),
        agentPhone: v.optional(v.union(v.string(), v.number())),
        baths: v.optional(v.number()),
        beds: v.optional(v.number()),
        daysOnMarket: v.optional(v.number()),
        lastStatusDate: v.optional(v.string()),
        price: v.optional(v.number()),
        propertyId: v.optional(v.number()),
        seqNo: v.optional(v.number()),
        status: v.optional(v.string()),
        statusDate: v.optional(v.string()),
        type: v.optional(v.string()),
      }),
      v.array(v.any())
    )),
    mlsKeywords: v.optional(v.any()),
    mlsLastSaleDate: v.optional(v.any()),
    mlsLastStatusDate: v.optional(v.any()),
    mlsListingDate: v.optional(v.any()),
    mlsListingPrice: v.optional(v.any()),
    mlsListingPricePerSquareFoot: v.optional(v.any()),
    mlsPending: v.optional(v.boolean()),
    mlsSold: v.optional(v.boolean()),
    mlsSoldPrice: v.optional(v.any()),
    mlsStatus: v.optional(v.any()),
    mlsType: v.optional(v.any()),
    mobileHome: v.optional(v.boolean()),
    mortgageHistory: v.optional(v.union(
      v.array(v.any()),
      v.any()
    )),
    neighborhood: v.optional(v.object({
      location: v.optional(v.object({
        coordinates: v.array(v.number()),
        type: v.literal("Point")
      })),
      id: v.optional(v.string()),
      name: v.optional(v.string()),
      type: v.optional(v.string())
    })),
    noticeType: v.optional(v.any()),
    openMortgageBalance: v.optional(v.number()),
    outOfStateAbsenteeOwner: v.optional(v.boolean()),
    ownerInfo: v.optional(v.object({
      absenteeOwner: v.optional(v.any()),
      companyName: v.optional(v.string()),
      corporateOwned: v.optional(v.any()),
      equity: v.optional(v.any()),
      inStateAbsenteeOwner: v.optional(v.any()),
      mailAddress: v.optional(v.object({
        address: v.optional(v.string()),
        addressFormat: v.optional(v.any()),
        carrierRoute: v.optional(v.string()),
        city: v.optional(v.string()),
        county: v.optional(v.string()),
        fips: v.optional(v.union(v.string(), v.number())),
        house: v.optional(v.number()),
        label: v.optional(v.string()),
        preDirection: v.optional(v.string()),
        state: v.optional(v.string()),
        street: v.optional(v.string()),
        streetType: v.optional(v.string()),
        unit: v.optional(v.string()),
        unitType: v.optional(v.string()),
        zip: v.optional(v.string()),
        zip4: v.optional(v.number()),
      })),
      outOfStateAbsenteeOwner: v.optional(v.any()),
      owner1FirstName: v.optional(v.any()),
      owner1FullName: v.optional(v.string()),
      owner1LastName: v.optional(v.string()),
      owner1Type: v.optional(v.string()),
      owner2FirstName: v.optional(v.any()),
      owner2FullName: v.optional(v.any()),
      owner2LastName: v.optional(v.any()),
      owner2Type: v.optional(v.any()),
      ownerOccupied: v.optional(v.any()),
      ownershipLength: v.optional(v.number()),
    })),
    ownerOccupied: v.optional(v.boolean()),
    preForeclosure: v.optional(v.boolean()),
    privateLender: v.optional(v.boolean()),
    propertyInfo: v.optional(v.object({
      address: v.optional(v.object({
        address: v.optional(v.string()),
        carrierRoute: v.optional(v.string()),
        city: v.optional(v.string()),
        congressionalDistrict: v.optional(v.union(v.string(), v.number())),
        county: v.optional(v.string()),
        fips: v.optional(v.union(v.string(), v.number())),
        house: v.optional(v.number()),
        jurisdiction: v.optional(v.string()),
        label: v.optional(v.string()),
        preDirection: v.optional(v.any()),
        state: v.optional(v.string()),
        street: v.optional(v.string()),
        streetType: v.optional(v.string()),
        unit: v.optional(v.any()),
        unitType: v.optional(v.any()),
        zip: v.optional(v.string()),
        zip4: v.optional(v.number()),
      })),
      airConditioningType: v.optional(v.any()),
      attic: v.optional(v.any()),
      basementFinishedPercent: v.optional(v.any()),
      basementSquareFeet: v.optional(v.number()),
      basementSquareFeetFinished: v.optional(v.any()),
      basementSquareFeetUnfinished: v.optional(v.any()),
      basementType: v.optional(v.string()),
      bathrooms: v.optional(v.number()),
      bedrooms: v.optional(v.number()),
      breezeway: v.optional(v.any()),
      buildingSquareFeet: v.optional(v.number()),
      buildingsCount: v.optional(v.any()),
      carport: v.optional(v.boolean()),
      construction: v.optional(v.string()),
      deck: v.optional(v.boolean()),
      deckArea: v.optional(v.number()),
      featureBalcony: v.optional(v.any()),
      fireplace: v.optional(v.boolean()),
      fireplaces: v.optional(v.number()),
      garageSquareFeet: v.optional(v.number()),
      garageType: v.optional(v.any()),
      heatingFuelType: v.optional(v.any()),
      heatingType: v.optional(v.string()),
      interiorStructure: v.optional(v.any()),
      latitude: v.optional(v.number()),
      livingSquareFeet: v.optional(v.number()),
      longitude: v.optional(v.number()),
      lotSquareFeet: v.optional(v.number()),
      parcelAccountNumber: v.optional(v.any()),
      parkingSpaces: v.optional(v.number()),
      partialBathrooms: v.optional(v.number()),
      patio: v.optional(v.boolean()),
      patioArea: v.optional(v.number()),
      plumbingFixturesCount: v.optional(v.number()),
      pool: v.optional(v.boolean()),
      poolArea: v.optional(v.number()),
      porchArea: v.optional(v.number()),
      porchType: v.optional(v.any()),
      pricePerSquareFoot: v.optional(v.number()),
      propertyUse: v.optional(v.string()),
      propertyUseCode: v.optional(v.number()),
      roofConstruction: v.optional(v.any()),
      roofMaterial: v.optional(v.any()),
      roomsCount: v.optional(v.number()),
      rvParking: v.optional(v.boolean()),
      safetyFireSprinklers: v.optional(v.boolean()),
      stories: v.optional(v.number()),
      taxExemptionHomeownerFlag: v.optional(v.any()),
      unitsCount: v.optional(v.number()),
      utilitiesSewageUsage: v.optional(v.any()),
      utilitiesWaterSource: v.optional(v.any()),
      yearBuilt: v.optional(v.number()),
    })),
    propertyType: v.optional(v.string()),
    quitClaim: v.optional(v.boolean()),
    reapi_loaded_at: v.optional(v.any()),
    saleHistory: v.optional(v.union(v.array(v.any()), v.any())),
    schools: v.optional(v.union(
      v.object({
        city: v.optional(v.string()),
        enrollment: v.optional(v.number()),
        grades: v.optional(v.string()),
        levels: v.optional(v.object({
          elementary: v.optional(v.boolean()),
          high: v.optional(v.boolean()),
          middle: v.optional(v.boolean()),
          preschool: v.optional(v.boolean()),
        })),
        location: v.optional(v.string()),
        name: v.optional(v.string()),
        parentRating: v.optional(v.number()),
        rating: v.optional(v.number()),
        state: v.optional(v.string()),
        street: v.optional(v.string()),
        type: v.optional(v.string()),
        zip: v.optional(v.string()),
      }),
      v.array(v.any())
    )),
    sheriffsDeed: v.optional(v.boolean()),
    spousalDeath: v.optional(v.boolean()),
    taxInfo: v.optional(v.object({
      assessedImprovementValue: v.optional(v.number()),
      assessedLandValue: v.optional(v.number()),
      assessedValue: v.optional(v.number()),
      assessmentYear: v.optional(v.number()),
      estimatedValue: v.optional(v.any()),
      marketImprovementValue: v.optional(v.number()),
      marketLandValue: v.optional(v.number()),
      marketValue: v.optional(v.number()),
      propertyId: v.optional(v.number()),
      taxAmount: v.optional(v.number()),
      taxDelinquentYear: v.optional(v.any()),
      year: v.optional(v.number()),
    })),
    taxLien: v.optional(v.boolean()),
    trusteeSale: v.optional(v.boolean()),
    vacant: v.optional(v.boolean()),
    warrantyDeed: v.optional(v.boolean()),
    source: v.optional(v.string()),
  },
  async handler(ctx, args) {
    const currentUser = await user(ctx);

    if (!currentUser) {
      throw new ConvexError("Not authenticated");
    }

    let reapiTag: Id<"tags"> | undefined;

    // Get the tag from the database
    const tagResult = await ctx.db
      .query("tags")
      .withIndex("by_orgId_name", (q) => q.eq("orgId", args.orgId).eq("name", "REAPI"))
      .first();

    if (tagResult) {
      reapiTag = tagResult._id;
    }

    // If the tag doesn't exist, create it
    if (!reapiTag) {
      const newTagId = await ctx.db.insert("tags", {
        userId: currentUser._id,
        orgId: args.orgId,
        name: "REAPI",
        recordType: "properties",
        color: getRandomColor(),
        userIds: [{
          userId: currentUser._id,
          role: "tag:admin",
        }],
      });
      reapiTag = (await ctx.db.get(newTagId))?._id ?? undefined;
    }

    // Ensure mlsHistory is an array
    let mlsHistory = args.mlsHistory ? (Array.isArray(args.mlsHistory) ? args.mlsHistory : [args.mlsHistory]) : [];

    console.log("Creating a new property", args.propertyInfo);

    // Create a property
    const propertyId = await ctx.db.insert("properties", {
      name: args.propertyInfo?.address?.address ?? "New REAPI Property",
      recordType: "properties",
      isDeleted: false,
      orgId: args.orgId,
      propertyType: args.propertyType,
      address: {
        street: args.propertyInfo?.address?.address || `${args.propertyInfo?.address?.house || ''} ${args.propertyInfo?.address?.street || ''} ${args.propertyInfo?.address?.streetType || ''}`.trim(),
        city: args.propertyInfo?.address?.city,
        state: args.propertyInfo?.address?.state,
        zip: args.propertyInfo?.address?.zip ? parseFloat(args.propertyInfo?.address?.zip) : undefined,
        county: args.propertyInfo?.address?.county,
      },
      location: {
        type: "Point",
        coordinates: [args.propertyInfo?.longitude ?? 0, args.propertyInfo?.latitude ?? 0],
      },
      updatedTime: args.lastSaleDate,
      tags: reapiTag ? [reapiTag] : [],
      createdBy: currentUser._id,
      yearBuilt: args.propertyInfo?.yearBuilt,
      squareFootage: args.propertyInfo?.buildingSquareFeet,
      units: args.propertyInfo?.unitsCount,
      equity: args.equity,
      parcelNumber: args.lotInfo?.apn,
      saleDate: args.lastSaleDate,
      salePrice: args.lastSalePrice ? Number(args.lastSalePrice) : undefined,
      floors: args.propertyInfo?.stories,
      landValue: args.taxInfo?.assessedLandValue,
      buildingValue: args.taxInfo?.assessedValue,
      status: mlsHistory?.[0]?.status,
      primaryUse: args.propertyInfo?.propertyUse,
      construction: args.propertyInfo?.construction,
      lotSize: args.lotInfo?.lotSquareFeet,
      lotType: args.lotInfo?.landUse,
      zoning: args.lotInfo?.zoning,
      class: args.lotInfo?.propertyClass,
      parking: args.propertyInfo?.parkingSpaces?.toString(),
      reaId: args.id?.toString(),
      absenteeOwner: args.absenteeOwner,
      auction: args.auction,
      cashBuyer: args.cashBuyer,
      corporateOwned: args.corporateOwned,
      deedInLieu: args.deedInLieu,
      equityPercent: args.equityPercent,
      estimatedEquity: args.estimatedEquity,
      estimatedValue: args.estimatedValue,
      floodZone: args.floodZone,
      floodZoneDescription: args.floodZoneDescription,
      floodZoneType: args.floodZoneType,
      freeClear: args.freeClear,
      highEquity: args.highEquity,
      inStateAbsenteeOwner: args.inStateAbsenteeOwner,
      inherited: args.inherited,
      investorBuyer: args.investorBuyer,
      lastSaleDate: args.lastSaleDate,
      lastSalePrice: args.lastSalePrice ? Number(args.lastSalePrice) : undefined,
      lastUpdateDate: args.lastUpdateDate,
      lien: args.lien,
      mlsActive: args.mlsActive,
      mlsCancelled: args.mlsCancelled,
      mlsDaysOnMarket: args.mlsDaysOnMarket,
      mlsFailed: args.mlsFailed,
      mlsHasPhotos: args.mlsHasPhotos,
      mlsListingDate: args.mlsListingDate,
      mlsListingPrice: args.mlsListingPrice,
      mlsListingPricePerSquareFoot: args.mlsListingPricePerSquareFoot,
      mlsPending: args.mlsPending,
      mlsSold: args.mlsSold,
      mlsSoldPrice: args.mlsSoldPrice,
      mlsStatus: args.mlsStatus,
      mlsType: args.mlsType,
      mobileHome: args.mobileHome,
      noticeType: args.noticeType,
      outOfStateAbsenteeOwner: args.outOfStateAbsenteeOwner,
      ownerOccupied: args.ownerOccupied,
      preForeclosure: args.preForeclosure,
      privateLender: args.privateLender,
      quitClaim: args.quitClaim,
      sheriffsDeed: args.sheriffsDeed,
      spousalDeath: args.spousalDeath,
      taxLien: args.taxLien,
      trusteeSale: args.trusteeSale,
      vacant: args.vacant,
      warrantyDeed: args.warrantyDeed,
      neighborhood: {
        location: {
          type: "Point",
          coordinates: args.neighborhood?.location?.coordinates ?? [],
        },
        id: args.neighborhood?.id,
        name: args.neighborhood?.name,
        type: args.neighborhood?.type,
      },
      bathrooms: args.propertyInfo?.bathrooms,
      bedrooms: args.propertyInfo?.bedrooms,
      buildingSquareFeet: args.propertyInfo?.buildingSquareFeet,
      carport: args.propertyInfo?.carport,
      garageSquareFeet: args.propertyInfo?.garageSquareFeet,
      garageType: args.propertyInfo?.garageType,
      heatingType: args.propertyInfo?.heatingType,
      livingSquareFeet: args.propertyInfo?.livingSquareFeet,
      lotSquareFeet: args.propertyInfo?.lotSquareFeet,
      parkingSpaces: args.propertyInfo?.parkingSpaces,
      pricePerSquareFoot: args.propertyInfo?.pricePerSquareFoot,
      propertyUse: args.propertyInfo?.propertyUse,
      roomsCount: args.propertyInfo?.roomsCount,
      legalDescription: args.lotInfo?.legalDescription,
      lotAcres: args.lotInfo?.lotAcres ? Number(args.lotInfo?.lotAcres) : undefined,
      lotNumber: args.lotInfo?.lotNumber ? String(args.lotInfo.lotNumber) : undefined,
      subdivision: args.lotInfo?.subdivision ? String(args.lotInfo?.subdivision) : undefined,
      taxInfo: {
        ...args.taxInfo,
      }
    });

    // demographics
    const demographicsId = await ctx.db.insert("demographics", {
      propertyId,
      orgId: args.orgId,
      ...args.demographics,
    });

    // foreclosure info
    const foreclosureInfoId = await ctx.db.insert("foreclosureInfo", {
      propertyId,
      orgId: args.orgId,
      ...args.foreclosureInfo,
    });

    // mls history
    let mlsHistoryId: Id<"mlsHistory"> | undefined;
    if (mlsHistory.length > 0) {
      mlsHistoryId = await ctx.db.insert("mlsHistory", {
        propertyId,
        orgId: args.orgId,
        mlsId: parseFloat(mlsHistory[0].propertyId.toString()),
        agentEmail: mlsHistory[0].agentEmail,
        agentName: mlsHistory[0].agentName,
        agentOffice: mlsHistory[0].agentOffice,
        agentPhone: mlsHistory[0].agentPhone?.toString(),
        baths: mlsHistory[0].baths,
        beds: mlsHistory[0].beds,
        daysOnMarket: mlsHistory[0].daysOnMarket,
        lastStatusDate: mlsHistory[0].lastStatusDate,
        price: mlsHistory[0].price,
        status: mlsHistory[0].status,
        statusDate: mlsHistory[0].statusDate,
        type: mlsHistory[0].type,
      });
    }

    // mortgages
    let mortgages: Id<"mortgages">[] = [];
    const mortgageHistory = Array.isArray(args.mortgageHistory) ? args.mortgageHistory : [args.mortgageHistory];
    for (const mortgage of mortgageHistory || []) {
      if (mortgage) {
        const mortgageId = await ctx.db.insert("mortgages", {
          propertyId,
          orgId: args.orgId,
          deedType: mortgage.deedType?.toString() || "",
          documentNumber: mortgage.documentNumber?.toString() || "",
          mortgageId: mortgage.mortgageId?.toString() || "",
          ...mortgage,
        });
        mortgages.push(mortgageId);
      }
    }

    let companyId: Id<"companies"> | undefined;
    let ownerContactId: Id<"contacts"> | undefined;
    if (args.ownerInfo?.companyName) {
      const company = await ctx.db.query("companies")
        .withIndex("by_orgId", (q) => q.eq("orgId", args.orgId))
        .filter((q) => q.eq(q.field("name"), args?.ownerInfo?.companyName))
        .first();
      if (company) {
        companyId = company._id;
      } else {
        companyId = await ctx.db.insert("companies", {
          orgId: args.orgId,
          name: args.ownerInfo.companyName,
          recordType: "companies",
          createdBy: currentUser._id,
          isDeleted: false,
        });

        await ctx.db.insert("activities", {
          orgId: args.orgId,
          userId: currentUser._id,
          recordId: companyId,
          recordType: "companies",
          system: true,
          type: "api",
          message: `${args.ownerInfo.companyName} was created by ${currentUser.name}`,
        });

        // link company to property
        await ctx.db.insert("companiesLinkedProperties", {
          orgId: args.orgId,
          companyId,
          propertyId,
          relation: "Owner",
        });
      }
    } else {
      const firstName = args.ownerInfo?.owner1FirstName ? String(args.ownerInfo.owner1FirstName) : undefined;
      const lastName = args.ownerInfo?.owner1LastName ? String(args.ownerInfo.owner1LastName) : undefined;
      const fullName = firstName && lastName ? `${firstName} ${lastName}` :
        firstName ? firstName :
          lastName ? lastName :
            "Unknown Owner";

      ownerContactId = await ctx.db.insert("contacts", {
        orgId: args.orgId,
        firstName,
        lastName,
        fullName,
        recordType: "contacts",
        createdBy: currentUser._id,
        email: [],
        companyId,
        isDeleted: false,
      });

      // create activity for owner contact
      await ctx.db.insert("activities", {
        orgId: args.orgId,
        userId: currentUser._id,
        recordId: ownerContactId,
        recordType: "contacts",
        system: true,
        type: "api",
        message: `${args.ownerInfo?.owner1FirstName} ${args.ownerInfo?.owner1LastName} has been created as the owner of ${args.propertyInfo?.address?.address}`,
      });

      // link contact to property
      if (ownerContactId) {
        await ctx.db.insert("contactLinkedProperties", {
          orgId: args.orgId,
          contactId: ownerContactId,
          propertyId,
          relation: "Owner",
        });
      }
    }

    // sale history
    let saleHistory: Id<"saleHistory">[] = [];
    if (args.saleHistory) {
      const saleHistoryArray = Array.isArray(args.saleHistory) ? args.saleHistory : [args.saleHistory];
      for (const sale of saleHistoryArray) {
        const saleHistoryId = await ctx.db.insert("saleHistory", {
          orgId: args.orgId,
          propertyId,
          saleDate: sale.saleDate,
          salePrice: sale.saleAmount,
          transactionType: sale.transactionType,
          seller: sale.sellerNames,
          buyer: sale.buyerNames,
        });
        saleHistory.push(saleHistoryId);
      }
    }

    // update the property
    await ctx.db.patch(propertyId, {
      demographics: demographicsId,
      foreclosureInfo: [foreclosureInfoId],
      mlsHistory: mlsHistoryId ? [mlsHistoryId] : [],
      mortgages: [...mortgages],
      neighborhood: args.neighborhood,
      saleHistory,
      taxInfo: {
        ...args.taxInfo,
      }
    });

    // populate the organization to get the slug
    const organization = await ctx.db.get(args.orgId);

    if (!organization) {
      throw new ConvexError("Organization not found");
    }

    // create the property url
    const propertyUrl = `${process.env.SITE_URL}/${organization.slug}/properties/${propertyId}`;

    // create a chunk for the property
    await ctx.scheduler.runAfter(
      0, internal.ingest.extract.extractPropertyInfo,
      {
        propertyId,
        name: args.propertyInfo?.address?.address || "New REAPI Property",
        propertyUrl,
        userId: currentUser._id,
        street: args.propertyInfo?.address?.street,
        city: args.propertyInfo?.address?.city,
        state: args.propertyInfo?.address?.state,
        zip: args.propertyInfo?.address?.zip ? Number(args.propertyInfo?.address?.zip) : undefined,
        county: args.propertyInfo?.address?.county,
        ...args,
      }
    );

    // create an activity for the property
    await ctx.db.insert("activities", {
      orgId: args.orgId,
      userId: currentUser._id,
      recordId: propertyId,
      recordType: "properties",
      system: true,
      type: "api",
      message: `${args.propertyInfo?.address?.address} has been created by ${currentUser.name} using REAPI`,
    });

    return {
      success: true,
      message: "Property created successfully",
      propertyId,
    };
  }
});

export const getMergingPropertiesInfo = query({
  args: {
    propertyId: v.array(v.id("properties")),
    paginationOpts: paginationOptsValidator,
    queryType: v.union(
      v.literal("linkedContacts"),
      v.literal("activities"),
      v.literal("linkedCompanies"),
      v.literal("tasks"),
      v.literal("all")
    )
  },
  handler: async (ctx, args) => {
    let properties: any[] = [];

    for (const propertyId of args.propertyId) {
      const property = await ctx.db.get(propertyId);
      if (property) {
        properties.push(property);
      }
    }

    if (args.queryType === "linkedContacts" || args.queryType === "all") {
      const linkedContactsResult = await ctx.db
        .query("contactLinkedProperties")
        .filter((q) =>
          q.or(...args.propertyId.map(id =>
            q.eq(q.field("propertyId"), id)
          ))
        )
        .paginate(args.paginationOpts);

      console.log("linkedContactsResult", linkedContactsResult);

      return {
        properties,
        mergingLinkedContacts: linkedContactsResult.page,
        mergingLinkedContactsCount: linkedContactsResult.page.length,
        pagination: {
          isDone: linkedContactsResult.isDone,
          cursor: linkedContactsResult.continueCursor,
          totalCount: linkedContactsResult.page.length,
        }
      };
    }

    if (args.queryType === "activities") {
      const activitiesResult = await ctx.db
        .query("activities")
        .filter((q) =>
          q.and(
            q.or(...args.propertyId.map(id =>
              q.eq(q.field("recordId"), id)
            )),
            q.neq(q.field("type"), "system")
          )
        )
        .paginate(args.paginationOpts);

      return {
        properties,
        mergingActivities: activitiesResult.page,
        mergingActivitiesCount: activitiesResult.page.length,
        pagination: {
          isDone: activitiesResult.isDone,
          cursor: activitiesResult.continueCursor,
          totalCount: activitiesResult.page.length,
        }
      };
    }

    if (args.queryType === "linkedCompanies") {
      const linkedCompaniesResult = await ctx.db
        .query("companiesLinkedProperties")
        .filter((q) =>
          q.or(...args.propertyId.map(id =>
            q.eq(q.field("propertyId"), id)
          ))
        )
        .paginate(args.paginationOpts);

      return {
        properties,
        mergingLinkedCompanies: linkedCompaniesResult.page,
        mergingLinkedCompaniesCount: linkedCompaniesResult.page.length,
        pagination: {
          isDone: linkedCompaniesResult.isDone,
          cursor: linkedCompaniesResult.continueCursor,
          totalCount: linkedCompaniesResult.page.length,
        }
      };
    }

    if (args.queryType === "tasks" || args.queryType === "all") {
      const tasksResult = await ctx.db
        .query("tasks")
        .filter((q) =>
          q.or(...args.propertyId.map(id =>
            q.eq(q.field("linkedRecord"), id)
          ))
        )
        .paginate(args.paginationOpts);

      return {
        properties,
        mergingTasks: tasksResult.page,
        mergingTasksCount: tasksResult.page.length,
        pagination: {
          isDone: tasksResult.isDone,
          cursor: tasksResult.continueCursor,
          totalCount: tasksResult.page.length,
        }
      };
    }

    return {
      properties,
      mergingPropertiesCount: args.propertyId.length,
    };
  },
});

export const createPropertyFromMongo = mutation({
  args: propertyArgs,
  handler: async (ctx, args) => {
    const currentUser = await user(ctx);
    const currentOrg = await ctx.db.get(args.orgId) as any;
    const hasAccess = await hasAccessToOrg(ctx, args.orgId);

    if (!currentUser) throw new ConvexError("Not authenticated");
    if (!hasAccess) return [];

    const propertyId = await ctx.db.insert("properties", {
      ...args,
      isDeleted: false,
      createdBy: currentUser._id as Id<"users">,
      recordType: "properties",
      mongoId: args.mongoId,
      status: args.status,
      units: args.units,
      yearBuilt: args.yearBuilt,
      squareFootage: args.squareFootage,
      price: args.price,
      parcelNumber: args.parcelNumber,
      saleDate: args.saleDate,
      salePrice: args.salePrice,
      landValue: args.landValue,
      buildingValue: args.buildingValue,
      primaryUse: args.primaryUse,
      construction: args.construction,
      lotSize: args.lotSize,
      zoning: args.zoning,
      meterType: args.meterType,
      class: args.class,
      structures: args.structures,
      parking: args.parking,
      address: {
        street: args.address.street,
        street2: args.address.street2,
        city: args.address.city,
        state: args.address.state,
        zip: args.address.zip,
        county: args.address.county,
        country: args.address.country,
      },
    });

    const propertyUrl = `${process.env.SITE_URL}/${currentOrg.slug}/properties/${propertyId}`;

    await ctx.db.insert("activities", {
      userId: currentUser._id as Id<"users">,
      orgId: args.orgId,
      recordId: propertyId,
      system: true,
      type: "create",
      message: `${args.name} was created by ${currentUser.name}`,
    });

    await ctx.scheduler.runAfter(
      0,
      internal.ingest.extract.extractPropertyInfo,
      {
        propertyId,
        name: args.name,
        propertyUrl,
        userId: currentUser._id,
        orgId: args.orgId,
        street: args.address.street,
        street2: args.address.street2,
        city: args.address.city,
        state: args.address.state,
        zip: args.address.zip,
      }
    );

    return propertyId;
  },
});

export const getByMongoId = query({
  args: {
    mongoId: v.string(),
  },
  handler: async (ctx, args) => {
    const { mongoId } = args;

    const property = await ctx.db
      .query("properties")
      .withIndex("by_mongoId", (q) =>
        q
          .eq("mongoId", mongoId))
      .first();

    if (!property) {
      return null;
    }

    return {
      _id: property._id,
      mongoId: property.mongoId,
      name: property.name,
    };
  },
});

export const updatePropertyAddress = mutation({
  args: {
    id: v.id("properties"),
    street: v.optional(v.string()),
    city: v.optional(v.string()),
    state: v.optional(v.string()),
    zip: v.optional(v.float64()),
  },
  async handler(ctx, args) {
    const { id, street, city, state, zip } = args;

    try {
      console.log("Updating property address:", { id, street, city, state, zip });

      const property = await ctx.db.get(id);

      if (!property) {
        console.log("Property not found:", id);
        throw new ConvexError("Property not found");
      }

      console.log("Existing property:", JSON.stringify(property, null, 2));

      const updatedAddress = {
        street: street || property.address?.street,
        city: city || property.address?.city,
        state: state || property.address?.state,
        zip: zip || property.address?.zip,
      };

      console.log("Updated address:", updatedAddress);

      await ctx.db.patch(id, {
        address: updatedAddress,
      });

      console.log("Address updated successfully");

      return { success: true, updatedAddress };
    } catch (error) {
      console.error("Error in updatePropertyAddress:", error);
      if (error instanceof ConvexError) {
        throw error;
      }
      throw new ConvexError(`Failed to update property address: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

export const getMongoIdMapping = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("properties")
      .filter(q => q.neq(q.field("mongoId"), undefined))
      .collect();
  }
});

export const syncPropertiesAggregate = internalMutation({
  args: {
    orgId: v.id("organization"),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const result = await ctx.db
      .query("properties")
      .withIndex("by_orgId", q => q.eq("orgId", args.orgId))
      .filter(q => q.eq(q.field("isDeleted"), false))
      .paginate({
        numItems: 100,
        cursor: args.cursor ?? null,
      });

    for (const property of result.page) {
      try {
        await aggregateProperties.insert(ctx, property);
      } catch (error) {
        if (error instanceof ConvexError && error.message.includes("already exists")) {
          continue;
        }
        throw error;
      }
    }

    if (!result.isDone) {
      const nextCursor = result.pageStatus === "SplitRecommended" || result.pageStatus === "SplitRequired"
        ? result.splitCursor ?? result.continueCursor
        : result.continueCursor;

      await ctx.scheduler.runAfter(0, internal.properties.syncPropertiesAggregate, {
        orgId: args.orgId,
        cursor: nextCursor
      });
    }

    return "Processing properties batch";
  },
});

export function getTotalPropertiesCount(ctx: QueryCtx, args: any) {
  return aggregateProperties.count(ctx, {
    namespace: undefined,
    bounds: {
      prefix: [args.orgId],
    }
  });
}

export const getCount = query({
  args: {
    orgId: v.id("organization"),
  },
  handler: async (ctx, args) => {
    return getTotalPropertiesCount(ctx, { orgId: args.orgId });
  }
});

/**
 * Query properties within a specific geographic bounding box
 * Used for the map view to display properties in the current viewport
 */
export const getPropertiesInBounds = query({
  args: {
    orgId: v.id("organization"),
    north: v.number(),
    south: v.number(),
    east: v.number(),
    west: v.number(),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
    filters: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const { orgId, north, south, east, west, limit = 50, cursor } = args;

    try {
      // Get properties for this organization
      const query = ctx.db
        .query("properties")
        .withIndex("by_orgId", (q) => q.eq("orgId", orgId));

      // Fetch properties - ensure we don't fetch too many to avoid OOM issues
      const properties = await query.take(250);

      // Filter to only properties within bounds - we have to do this in memory
      // since we can't directly query on nested fields in Convex
      const inBoundsProperties = properties.filter(p => {
        if (!p.location?.coordinates || p.location.coordinates.length < 2) return false;

        const lng = p.location.coordinates[0];
        const lat = p.location.coordinates[1];
        return lat >= south && lat <= north && lng >= west && lng <= east;
      });

      // Apply any additional filters from the request
      let filtered = inBoundsProperties;
      if (args.filters) {
        const { filters } = args;

        if (filters.status) {
          filtered = filtered.filter(p => p.status === filters.status);
        }

        if (filters.propertyType) {
          filtered = filtered.filter(p => p.propertyType === filters.propertyType);
        }

        if (filters.minBeds) {
          filtered = filtered.filter(p =>
            typeof p.bedrooms === 'number' && p.bedrooms >= filters.minBeds
          );
        }

        if (filters.maxBeds) {
          filtered = filtered.filter(p =>
            typeof p.bedrooms === 'number' && p.bedrooms <= filters.maxBeds
          );
        }
      }

      // Handle pagination
      const startIndex = cursor ? parseInt(cursor, 10) : 0;
      const endIndex = Math.min(startIndex + limit, filtered.length);
      const pageItems = filtered.slice(startIndex, endIndex);
      const nextCursor = endIndex < filtered.length ? String(endIndex) : null;

      return {
        properties: pageItems,
        continueCursor: nextCursor,
        isDone: endIndex >= filtered.length,
      };

    } catch (error) {
      console.error("Error in getPropertiesInBounds:", error);
      return { properties: [], continueCursor: null, isDone: true };
    }
  },
});