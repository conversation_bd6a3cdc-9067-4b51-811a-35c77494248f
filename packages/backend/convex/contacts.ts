import { paginationOptsValidator } from "convex/server";
import { ConvexError, v } from "convex/values";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { internalMutation, mutation, query, QueryCtx } from "./_generated/server";
import { aggregateContacts } from "./custom";
import { hasAccessToOrg, user } from "./helpers/standardHelpers";
import { recordViewed, updateRecord } from "./records";
// Simple server-side filtering function for contacts
function applyServerFilters(query: any, filters: any[]) {
  if (!filters || filters.length === 0) return query;

  return query.filter((q: any) => {
    const conditions = filters.map((filter) => {
      const { field, operator, value } = filter;

      console.log(`Applying contact filter: ${field} ${operator} ${value}`);

      // Handle email field (array of objects)
      if (field === 'email') {
        switch (operator) {
          case 'contains':
            return q.some(q.field('email'), (item: any) =>
              q.and(
                q.neq(item.field('address'), null),
                q.neq(item.field('address'), ""),
                q.includes(item.field('address'), value.toString())
              )
            );
          case 'equals':
            return q.some(q.field('email'), (item: any) =>
              q.eq(item.field('address'), value)
            );
          default:
            return q.some(q.field('email'), (item: any) =>
              q.includes(item.field('address'), value.toString())
            );
        }
      }

      // Handle phone field (array of objects)
      if (field === 'phone') {
        switch (operator) {
          case 'contains':
            return q.some(q.field('phone'), (item: any) =>
              q.and(
                q.neq(item.field('number'), null),
                q.neq(item.field('number'), ""),
                q.includes(item.field('number'), value.toString())
              )
            );
          case 'equals':
            return q.some(q.field('phone'), (item: any) =>
              q.eq(item.field('number'), value)
            );
          default:
            return q.some(q.field('phone'), (item: any) =>
              q.includes(item.field('number'), value.toString())
            );
        }
      }

      // Handle regular fields
      if (field === 'fullName' || field === 'firstName' || field === 'lastName') {
        switch (operator) {
          case 'contains':
            return q.and(
              q.neq(q.field(field), null),
              q.includes(q.field(field), value.toString())
            );
          case 'equals':
            return q.eq(q.field(field), value);
          default:
            return q.includes(q.field(field), value.toString());
        }
      }

      if (field === 'status' || field === 'persona') {
        switch (operator) {
          case 'equals':
            return q.eq(q.field(field), value);
          case 'isAnyOf':
            if (Array.isArray(value)) {
              return q.or(...value.map((v: any) => q.eq(q.field(field), v)));
            }
            return q.eq(q.field(field), value);
          default:
            return q.eq(q.field(field), value);
        }
      }

      // Default case
      switch (operator) {
        case 'contains':
          return q.and(
            q.neq(q.field(field), null),
            q.includes(q.field(field), value.toString())
          );
        case 'equals':
          return q.eq(q.field(field), value);
        case 'isAnyOf':
          if (Array.isArray(value)) {
            return q.or(...value.map((v: any) => q.eq(q.field(field), v)));
          }
          return q.eq(q.field(field), value);
        default:
          return q.eq(q.field(field), value);
      }
    });

    return q.and(...conditions);
  });
}

const contactArgs = {
  firstName: v.optional(v.string()),
  lastName: v.optional(v.string()),
  fullName: v.string(),
  email: v.array(
    v.object({
      label: v.optional(v.string()),
      address: v.optional(v.string()),
      isBad: v.optional(v.boolean()),
      isPrimary: v.optional(v.boolean()),
    })
  ),
  orgId: v.id("organization"),
  mongoId: v.optional(v.string()),
  recordType: v.optional(v.literal("contacts")),
  address: v.optional(v.array(
    v.object({
      label: v.optional(v.string()),
      street: v.optional(v.string()),
      city: v.optional(v.string()),
      state: v.optional(v.string()),
      zip: v.optional(v.number()),
    })
  )),
  phone: v.optional(v.array(
    v.object({
      label: v.optional(v.string()),
      number: v.optional(v.string()),
      isBad: v.optional(v.boolean()),
      isPrimary: v.optional(v.boolean()),
    })
  )),
  stage: v.optional(v.string()),
  status: v.optional(v.string()),
  summary: v.optional(v.string()),
  aiGeneratedSummary: v.optional(v.string()),
};

export async function createContact(ctx: any, args: any, enrichment = false) {
  const currentUser = await user(ctx);
  const currentOrg = (await ctx.db.get(args.orgId)) as any;
  const hasAccess = await hasAccessToOrg(ctx, args.orgId);

  if (!currentUser) throw new ConvexError("Not authenticated");
  if (!hasAccess) return [];

  args.firstName = args.firstName || "No";
  args.lastName = args.lastName || "Name";

  const contactId = await ctx.db.insert("contacts", {
    ...args,
    isDeleted: false,
    createdBy: currentUser?._id as Id<"users">,
    recordType: "contacts",
    mongoId: args.mongoId,
    fullName: `${args.firstName} ${args.lastName}`.trim(),
    email: args.email || [],
    phone: args.phone || [],
    address: args.address || [],
    summary: args.summary || "",
    aiGeneratedSummary: args.aiGeneratedSummary || "",
    stage: args.stage || "",
    status: args.status || "",
    updatedTime: new Date().toISOString(),
    updatedBy: currentUser?._id as Id<"users">,
    lastViewedTime: Date.now(),
    lastViewedBy: currentUser?._id as Id<"users">,
  });

  const contactUrl = `${process.env.SITE_URL}/${currentOrg.slug}/contacts/${contactId}`;

  await ctx.db.insert("activities", {
    userId: currentUser?._id,
    orgId: args.orgId,
    recordId: contactId,
    system: true,
    type: "create",
    message: `${args.fullName} was created by ${currentUser?.name}`,
  });

  await ctx.scheduler.runAfter(
    0,
    enrichment
      ? internal.ingest.extract.extractContactInfoForEnrichment
      : internal.ingest.extract.extractContactInfo,
    {
      contactUrl,
      id: contactId,
      userId: currentUser?._id,
      orgId: args.orgId,
      fullName: args.fullName,
      email: args.email?.[0]?.address || "",
    }
  );

  return contactId;
}

export const createContactWithEnrichment = mutation({
  args: contactArgs,
  handler: (ctx, args) => createContact(ctx, args, true),
});

export const createContactWithoutEnrichment = mutation({
  args: contactArgs,
  handler: (ctx, args) => createContact(ctx, args, false),
});

export const contactViewed = mutation({
  args: {
    id: v.id("contacts"),
    orgId: v.id("organization"),
  },
  handler: async (ctx, args) => {
    await recordViewed(ctx, {
      ...args,
      recordType: "contacts",
    });

    return {
      message: "Contact viewed successfully",
      status: "success",
    }
  }
});

export const updateContact = mutation({
  args: {
    id: v.id("contacts"),
    orgId: v.id("organization"),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    fullName: v.optional(v.string()),
    phone: v.optional(
      v.array(
        v.object({
          number: v.string(),
          label: v.string(),
          isBad: v.boolean(),
          isPrimary: v.boolean(),
        })
      )
    ),
    email: v.optional(
      v.array(
        v.object({
          address: v.string(),
          label: v.string(),
          isBad: v.boolean(),
          isPrimary: v.boolean(),
        })
      )
    ),
    address: v.optional(
      v.array(
        v.object({
          label: v.optional(v.string()),
          street: v.optional(v.string()),
          street2: v.optional(v.string()),
          city: v.optional(v.string()),
          state: v.optional(v.string()),
          zip: v.optional(v.number()),
          county: v.optional(v.string()),
          country: v.optional(v.string()),
        })
      )
    ),
    buyerNeeds: v.optional(
      v.object({
        propertyType: v.optional(v.string()),
        minUnits: v.optional(v.number()),
        maxUnits: v.optional(v.number()),
        minPrice: v.optional(v.number()),
        maxPrice: v.optional(v.number()),
        minEquity: v.optional(v.number()),
        maxEquity: v.optional(v.number()),
        exchange: v.optional(v.boolean()),
        exchangeId: v.optional(v.string()),
        cap: v.optional(v.number()),
        yearBuiltFrom: v.optional(v.number()),
        yearBuiltTo: v.optional(v.number()),
      })
    ),
    title: v.optional(v.string()),
    status: v.optional(v.string()),
    persona: v.optional(v.string()),
    source: v.optional(v.string()),
    stage: v.optional(v.string()),
    website: v.optional(v.string()),
    spouseName: v.optional(v.string()),
    birthday: v.optional(v.string()),
    summary: v.optional(v.string()),
    aiGeneratedSummary: v.optional(v.string()),
    isDeleted: v.optional(v.boolean()),
    deletedBy: v.optional(v.id("users")),
    deletedTime: v.optional(v.string()),
    age: v.optional(v.float64())
  },
  async handler(ctx, args) {
    const { id, orgId, ...updateFields } = args;
    const contact = await updateRecord(ctx, {
      id,
      orgId,
      updateFields,
      recordType: "contacts",
    });

    return {
      message: "Contact updated successfully",
      status: "success",
      data: contact,
    }
  }
});

export const updateBatchContacts = mutation({
  args: {
    ids: v.array(v.id("contacts")),
  },
  handler: async (ctx, args) => {
    const { ids } = args;

    const currentUser = await user(ctx);

    if (!currentUser) {
      throw new ConvexError("Not authenticated");
    }

    for (const id of ids) {
      await ctx.db.patch(id, {
        isDeleted: true,
        updatedBy: currentUser._id,
        updatedTime: new Date().toISOString(),
        deletedBy: currentUser._id,
        deletedTime: new Date().toISOString(),
      });
    }
  }
});

export const deleteContact = mutation({
  args: { id: v.id("contacts") },
  handler: async (ctx, args) => {
    const { id } = args;

    const chunks = await ctx.db.query("chunks")
      .withIndex("by_contactId", (q) => q.eq("contactId", id))
      .first();

    const embeddings = await ctx.db.query("embeddings")
      .withIndex("by_chunkId", (q) => q.eq("chunkId", chunks?._id as Id<"chunks">))
      .first();

    await ctx.db.delete(embeddings?._id as Id<"embeddings">);
    await ctx.db.delete(chunks?._id as Id<"chunks">);

    await ctx.db.delete(id);
  },
});

export const getContact = query({
  args: {
    id: v.id("contacts"),
    orgId: v.optional(v.id("organization")),
    isDeleted: v.optional(v.boolean()),
  },
  async handler(ctx, args) {
    const hasAccess = await hasAccessToOrg(
      ctx,
      args.orgId as Id<"organization">
    );

    if (!hasAccess) {
      return [];
    }

    const contact = (await ctx.db.get(args.id)) as any;

    if (!contact) {
      throw new ConvexError("Contact not found");
    }

    const activities = await ctx.db
      .query("activities")
      .withIndex("by_recordId", (q) => q.eq("recordId", args.id))
      .collect();

    const createdBy = contact.createdBy
      ? await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("_id"), contact.createdBy as Id<"users">))
        .first()
      : null;

    const lastViewedBy = contact.lastViewedBy
      ? await ctx.db
        .query("users")
        .filter((q) =>
          q.eq(q.field("_id"), contact.lastViewedBy as Id<"users">)
        )
        .first()
      : null;

    const updatedBy = contact.updatedBy
      ? await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("_id"), contact.updatedBy as Id<"users">))
        .first()
      : null;

    return {
      ...contact,
      activities,
      createdBy,
      lastViewedBy,
      updatedBy,
    };
  },
});

export const getContactCompanies = query({
  args: {
    id: v.id("contacts"),
    orgId: v.id("organization"),
  },
  async handler(ctx, args) {
    const hasAccess = await hasAccessToOrg(ctx, args.orgId);

    if (!hasAccess) {
      return [];
    }

    const contact = await ctx.db.get(args.id);

    if (!contact) {
      throw new ConvexError("Contact not found");
    }

    const companyId = contact.companyId;

    if (!companyId) {
      return null;
    }

    const company = await ctx.db.get(companyId as Id<"companies">);

    return company;
  },
});

export const getContacts = query({
  args: {
    orgId: v.optional(v.id("organization")),
    query: v.optional(v.string()),
    favorites: v.optional(v.boolean()),
    isDeleted: v.optional(v.boolean()),
    filters: v.optional(v.array(v.object({
      field: v.string(),
      operator: v.string(),
      value: v.union(v.string(), v.number(), v.boolean())
    }))),
    paginationOpts: paginationOptsValidator
  },
  async handler(ctx, args) {
    const currentUser = await user(ctx);
    if (!currentUser) {
      throw new ConvexError("Unauthorized");
    }

    const hasAccess = await hasAccessToOrg(
      ctx,
      currentUser.activeOrgId as Id<"organization">
    );

    if (!hasAccess) {
      return { page: [], isDone: true, totalCount: 0 };
    }

    let contactsQuery = ctx.db
      .query("contacts")
      .withIndex("by_orgId", (q) =>
        q.eq("orgId", currentUser.activeOrgId as Id<"organization">)
      )
      .filter((q) => q.eq(q.field("isDeleted"), args.isDeleted || false));

    // Apply text search if query is provided
    if (args.query) {
      const queryValue = args.query.trim();
      if (isNaN(Number(queryValue))) {
        contactsQuery = contactsQuery.filter((q) =>
          q.or(
            q.eq(q.field("fullName"), queryValue),
            q.eq(q.field("firstName"), queryValue),
            q.eq(q.field("lastName"), queryValue),
          )
        );
      }
    }

    // Apply custom filters
    if (args.filters && args.filters.length > 0) {
      console.log('Applying filters to contacts query:', JSON.stringify(args.filters, null, 2));
      contactsQuery = applyServerFilters(contactsQuery, args.filters);
    }

    // Get the filtered count if filters are applied
    let totalCount;
    if (args.filters && args.filters.length > 0) {

      let countQuery = ctx.db
        .query("contacts")
        .withIndex("by_orgId", (q) =>
          q.eq("orgId", currentUser.activeOrgId as Id<"organization">)
        )
        .filter((q) => q.eq(q.field("isDeleted"), args.isDeleted || false));

      if (args.query) {
        const queryValue = args.query.trim();
        if (isNaN(Number(queryValue))) {
          countQuery = countQuery.filter((q) =>
            q.or(
              q.eq(q.field("fullName"), queryValue),
              q.eq(q.field("firstName"), queryValue),
              q.eq(q.field("lastName"), queryValue),
            )
          );
        }
      }

      if (args.filters && args.filters.length > 0) {
        countQuery = applyServerFilters(countQuery, args.filters);
      }

      const filteredResults = await countQuery.collect();
      totalCount = filteredResults.length;
      console.log("Filtered count:", totalCount);
    } else {
      // If no filters, get the total count
      totalCount = await getTotalContactsCount(ctx, { orgId: currentUser.activeOrgId });
      console.log("Total count:", totalCount);
    }

    const paginationResult = await contactsQuery.paginate(args.paginationOpts);

    if (args.favorites) {
      const favorites = await ctx.db
        .query("favorites")
        .withIndex("by_userId_orgId_recordId", (q) =>
          q
            .eq("userId", hasAccess.user._id)
            .eq("orgId", currentUser.activeOrgId as Id<"organization">)
        )
        .collect();

      paginationResult.page = paginationResult.page.filter((contact: any) =>
        favorites.some((favorite) => favorite.recordId === contact._id)
      );
    }

    return {
      page: paginationResult.page,
      isDone: paginationResult.isDone,
      cursor: paginationResult.continueCursor,
      totalCount
    };
  }
});

export const checkDuplicateEmail = query({
  args: {
    orgId: v.id("organization"),
    email: v.string(),
  },
  async handler(ctx, args) {
    const { orgId, email } = args;

    if (!email) {
      return null;
    }

    const existingContacts = await ctx.db
      .query("contacts")
      .withIndex("by_orgId", (q) => q.eq("orgId", orgId))
      .filter((q) =>
        q.eq("email[].address", args.email)
      )
      .collect();

    const duplicateContact = existingContacts.find((contact) =>
      contact.email.some(
        (e) => e.address.toLowerCase() === email.toLowerCase()
      )
    );

    return duplicateContact || null;
  },
});

export const patchContactSummary = internalMutation({
  args: {
    id: v.id("contacts"),
    summary: v.string(),
  },
  handler: async (ctx, { id, summary }) => {
    await ctx.db.patch(id, {
      summary: summary,
    });
  },
});

export const getMergingContactsInfo = query({
  args: {
    contactId: v.array(v.id("contacts")),
    paginationOpts: paginationOptsValidator,
    queryType: v.union(
      v.literal("relatedContacts"),
      v.literal("activities"),
      v.literal("linkedProperties"),
      v.literal("tasks"),
      v.literal("all")
    )
  },
  handler: async (ctx, args) => {
    let contacts: any[] = [];

    for (const contactId of args.contactId) {
      const contact = await ctx.db.get(contactId);
      if (contact) {
        contacts.push(contact);
      }
    }

    // Based on queryType, execute only the relevant query
    if (args.queryType === "relatedContacts" || args.queryType === "all") {
      const relatedContactsResult = await ctx.db
        .query("relatedContacts")
        .filter((q) =>
          q.or(...args.contactId.map(id =>
            q.eq(q.field("recordId"), id)
          ))
        )
        .paginate(args.paginationOpts);

      return {
        contacts,
        mergingRelatedContacts: relatedContactsResult.page,
        mergingRelatedContactsCount: relatedContactsResult.page.length,
        pagination: {
          isDone: relatedContactsResult.isDone,
          continueCursor: relatedContactsResult.continueCursor
        }
      };
    }

    if (args.queryType === "activities") {
      const activitiesResult = await ctx.db
        .query("activities")
        .filter((q) =>
          q.and(
            q.or(...args.contactId.map(id =>
              q.eq(q.field("recordId"), id)
            )),
            q.neq(q.field("type"), "system")
          )
        )
        .paginate(args.paginationOpts);

      return {
        contacts,
        mergingActivities: activitiesResult.page,
        mergingActivitiesCount: activitiesResult.page.length,
        pagination: {
          isDone: activitiesResult.isDone,
          continueCursor: activitiesResult.continueCursor
        }
      };
    }

    if (args.queryType === "linkedProperties") {
      const linkedPropertiesResult = await ctx.db
        .query("contactLinkedProperties")
        .filter((q) =>
          q.or(...args.contactId.map(id =>
            q.eq(q.field("contactId"), id)
          ))
        )
        .paginate(args.paginationOpts);

      return {
        contacts,
        mergingLinkedProperties: linkedPropertiesResult.page,
        mergingLinkedPropertiesCount: linkedPropertiesResult.page.length,
        pagination: {
          isDone: linkedPropertiesResult.isDone,
          continueCursor: linkedPropertiesResult.continueCursor
        }
      };
    }

    if (args.queryType === "tasks" || args.queryType === "all") {
      const tasksResult = await ctx.db
        .query("tasks")
        .filter((q) =>
          q.or(...args.contactId.map(id =>
            q.eq(q.field("linkedRecord"), id)
          ))
        )
        .paginate(args.paginationOpts);

      return {
        contacts,
        mergingTasks: tasksResult.page,
        mergingTasksCount: tasksResult.page.length,
        pagination: {
          isDone: tasksResult.isDone,
          continueCursor: tasksResult.continueCursor
        }
      };
    }

    return {
      contacts,
      mergingContactsCount: args.contactId.length,
    };
  },
});

export const mergeContacts = mutation({
  args: {
    primaryContactId: v.id("contacts"),
    contactId: v.array(v.id("contacts")),
  },
  handler: async (ctx, args) => {
    const currentUser = await user(ctx);

    if (!currentUser) {
      throw new ConvexError("Not authenticated");
    }

    const primaryContact = await ctx.db.get(args.primaryContactId);

    if (!primaryContact) {
      throw new ConvexError("Primary contact not found");
    }

    for (const contactId of args.contactId) {
      if (contactId === args.primaryContactId) continue;

      const contact = await ctx.db.get(contactId);

      if (contact) {
        // merge phone numbers
        primaryContact.phone = [...(primaryContact.phone || []), ...(contact.phone || [])];

        // merge emails
        primaryContact.email = [...(primaryContact.email || []), ...(contact.email || [])];

        // merge addresses
        primaryContact.address = [...(primaryContact.address || []), ...(contact.address || [])];

        // if no image, merge image
        if (!primaryContact.image) {
          primaryContact.image = contact.image;
        }

        // merge linked properties
        primaryContact.linkedProperties = [...(primaryContact.linkedProperties || []), ...(contact.linkedProperties || [])];

        // merge lists
        const primaryLists = primaryContact.lists || [];
        const contactLists = contact.lists || [];
        for (const list of contactLists) {
          if (!primaryLists.includes(list)) {
            await ctx.db.patch(args.primaryContactId, {
              lists: [...primaryLists, list],
            });
          }
        }

        // merge social
        const primarySocial = primaryContact.social || {};
        const contactSocial = contact.social || {};
        for (const [key, value] of Object.entries(contactSocial)) {
          if (!primarySocial[key as keyof typeof primarySocial]) {
            await ctx.db.patch(args.primaryContactId, {
              social: { ...primarySocial, [key as keyof typeof primarySocial]: value },
            });
          }
        }

        // if no summary, merge summary
        if (!primaryContact.summary) {
          primaryContact.summary = contact.summary;
        }

        // merge tags
        const primaryTags = primaryContact.tags || [];
        const contactTags = contact.tags || [];
        for (const tag of contactTags) {
          if (!primaryTags.includes(tag)) {
            await ctx.db.patch(args.primaryContactId, {
              tags: [...primaryTags, tag],
            });
          }
        }

        // if no birthday, merge birthday
        if (!primaryContact.birthday) {
          primaryContact.birthday = contact.birthday;
        }

        // if no buyer needs, merge buyer needs
        if (!primaryContact.buyerNeeds) {
          primaryContact.buyerNeeds = contact.buyerNeeds;
        }

        // if no companyId, merge companyId
        if (!primaryContact.companyId) {
          primaryContact.companyId = contact.companyId;
        }

        // merge linked contacts
        const primaryLinkedContacts = primaryContact.linkedContacts || [];
        const contactLinkedContacts = contact.linkedContacts || [];
        for (const linkedContact of contactLinkedContacts) {
          if (!primaryLinkedContacts.includes(linkedContact)) {
            await ctx.db.patch(args.primaryContactId, {
              linkedContacts: [...primaryLinkedContacts, linkedContact],
            });
          }
        }

        // if primary contact has no persona, merge persona
        if (!primaryContact.persona) {
          primaryContact.persona = contact.persona;
        }

        // merge source
        if (!primaryContact.source) {
          primaryContact.source = contact.source;
        }

        // merge spouse name
        if (!primaryContact.spouseName) {
          primaryContact.spouseName = contact.spouseName;
        }

        // merge stage
        if (!primaryContact.stage) {
          primaryContact.stage = contact.stage;
        }

        // if primary contact has no status, merge status
        if (!primaryContact.status) {
          primaryContact.status = contact.status;
        }

        // if primary contact has no title, merge title
        if (!primaryContact.title) {
          primaryContact.title = contact.title;
        }

        // if no website, merge website
        if (!primaryContact.website) {
          primaryContact.website = contact.website;
        }

        // merge tasks
        primaryContact.tasks = [...(primaryContact.tasks || []), ...(contact.tasks || [])];


        // Merge linked properties
        const linkedProperties = await ctx.db.query("contactLinkedProperties")
          .filter((q) => q.eq(q.field("contactId"), contactId))
          .collect();

        for (const linkedProperty of linkedProperties) {
          // Check if the primary contact already has this property linked
          const existingLink = await ctx.db.query("contactLinkedProperties")
            .filter((q) => q.eq(q.field("contactId"), args.primaryContactId))
            .filter((q) => q.eq(q.field("propertyId"), linkedProperty.propertyId))
            .first();

          if (!existingLink) {
            // If no existing link, create a new one for the primary contact
            await ctx.db.insert("contactLinkedProperties", {
              contactId: args.primaryContactId,
              propertyId: linkedProperty.propertyId,
              relation: linkedProperty.relation,
              orgId: linkedProperty.orgId,
            });
          }

          // Delete the old link
          await ctx.db.delete(linkedProperty._id);
        }

        // Delete the merged contact
        await ctx.db.delete(contactId);
      }
    }

    await ctx.db.insert("activities", {
      orgId: primaryContact.orgId,
      userId: currentUser._id,
      recordId: primaryContact._id,
      recordType: "contacts",
      system: true,
      type: "merge",
      message: `Merged ${args.contactId.length - 1} contact${args.contactId.length > 2 ? "s" : ""} into ${primaryContact.fullName}`,
    });

    return { message: "Contacts merged successfully", status: "success" };
  },
});

export const getByMongoId = query({
  args: {
    mongoId: v.string(),
  },
  handler: async (ctx, args) => {
    const contact = await ctx.db
      .query("contacts")
      .withIndex("by_mongoId", (q) => q
        .eq("mongoId", args.mongoId))
      .first();

    if (!contact) {
      return null;
    }

    return {
      _id: contact._id,
      mongoId: contact.mongoId,
      fullName: contact.fullName,
    };
  },
});

export const getOwnerContactIds = query({
  args: {
    propertyIds: v.array(v.id("properties")),
  },
  async handler(ctx, args) {
    const currentUser = await user(ctx);

    if (!currentUser) {
      throw new ConvexError("Not authenticated");
    }

    const orgId = currentUser.activeOrgId;

    if (!orgId) {
      throw new ConvexError("No access to organization");
    }

    const ownerIds: (Id<"contacts"> | Id<"companies">)[] = [];

    for (const propertyId of args.propertyIds) {
      const linkedContacts = await ctx.db
        .query("contactLinkedProperties")
        .filter((q) =>
          q.and(
            q.eq(q.field("orgId"), orgId),
            q.eq(q.field("propertyId"), propertyId),
            q.eq(q.field("relation"), "Owner")
          )
        )
        .collect();

      const linkedCompanies = await ctx.db
        .query("companiesLinkedProperties")
        .filter((q) =>
          q.and(
            q.eq(q.field("orgId"), orgId),
            q.eq(q.field("propertyId"), propertyId),
            q.eq(q.field("relation"), "Owner")
          )
        )
        .collect();

      // TODO: Get the initial company team member

      ownerIds.push(...linkedContacts.map((link) => link.contactId));
      ownerIds.push(...linkedCompanies.map((link) => link.companyId));
    }

    return ownerIds;
  },
});

export const getMongoIdMapping = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("contacts")
      .filter(q => q.neq(q.field("mongoId"), undefined))
      .collect();
  }
});

export const syncContactsAggregate = internalMutation({
  args: {
    orgId: v.id("organization"),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const result = await ctx.db
      .query("contacts")
      .withIndex("by_orgId", q => q.eq("orgId", args.orgId))
      .filter(q => q.eq(q.field("isDeleted"), false))
      .paginate({
        numItems: 100,
        cursor: args.cursor ?? null,
      });

    for (const contact of result.page) {
      try {
        await aggregateContacts.insert(ctx, contact);
      } catch (error) {
        if (error instanceof ConvexError && error.message.includes("already exists")) {
          continue;
        }
        throw error;
      }
    }

    if (!result.isDone) {
      const nextCursor = result.pageStatus === "SplitRecommended" || result.pageStatus === "SplitRequired"
        ? result.splitCursor ?? result.continueCursor
        : result.continueCursor;

      await ctx.scheduler.runAfter(0, internal.contacts.syncContactsAggregate, {
        orgId: args.orgId,
        cursor: nextCursor
      });
    }

    return "Processing contacts batch";
  },
});

export function getTotalContactsCount(ctx: QueryCtx, args: any) {
  return aggregateContacts.count(ctx, {
    namespace: undefined,
    bounds: {
      prefix: [args.orgId],
    }
  });
}